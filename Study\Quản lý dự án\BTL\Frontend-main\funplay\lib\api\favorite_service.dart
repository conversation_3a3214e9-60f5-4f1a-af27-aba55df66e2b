import 'dart:convert';
import '../utils/api_utils.dart';
import '../utils/user_preferences_utils.dart';
import '../models/post.dart';
import '../api/config.dart';

class FavoriteService {
  static const String favoritesEndpoint = '/favorite';

  // Add a post to favorites
  static Future<bool> addToFavorites(String postId) async {
    try {
      // Get user ID from shared preferences
      final userProfile = await UserPreferencesUtils.getUserProfile();
      print(userProfile);
      final userId = userProfile['id'];

      if (userId == null) {
        throw Exception('User not logged in');
      }
      print(userId);
      print(postId);

      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiBaseUrl}$favoritesEndpoint/create',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'user_id': userId,
          'post_id': postId,
        }),
      );

      final data = await ApiUtils.handleApiResponse(response);
      return data['success'] == true;
    } catch (e) {
      print('Error adding to favorites: $e');
      return false;
    }
  }

  // Remove a post from favorites
  static Future<bool> removeFromFavorites(String postId) async {
    try {
      // Get user ID from shared preferences
      final userProfile = await UserPreferencesUtils.getUserProfile();
      final userId = userProfile['id'];

      if (userId == null) {
        throw Exception('User not logged in');
      }

      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiBaseUrl}$favoritesEndpoint/remove',
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'user_id': userId,
          'post_id': postId,
        }),
      );

      final data = await ApiUtils.handleApiResponse(response);
      return data['success'] == true;
    } catch (e) {
      print('Error removing from favorites: $e');
      return false;
    }
  }

  // Get all favorites for the current user (post IDs only)
  static Future<List<String>> getUserFavorites() async {
    try {
      // Get user ID from shared preferences
      final userProfile = await UserPreferencesUtils.getUserProfile();
      final userId = userProfile['id'];

      if (userId == null) {
        throw Exception('User not logged in');
      }

      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiBaseUrl}$favoritesEndpoint/user/$userId',
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      );

      final data = await ApiUtils.handleApiResponse(response);

      if (data['success'] == true && data['data'] != null) {
        // Extract post IDs from the favorite data
        List<String> favoritePostIds = [];
        for (var favorite in data['data']) {
          favoritePostIds.add(favorite['post_id'].toString());
        }
        return favoritePostIds;
      }

      return [];
    } catch (e) {
      print('Error getting user favorites: $e');
      return [];
    }
  }

  // NEW METHOD: Get all favorites with full post details
  static Future<List<dynamic>> getUserFavoritesWithDetails() async {
    try {
      // Get user ID from shared preferences
      final userProfile = await UserPreferencesUtils.getUserProfile();
      final userId = userProfile['id'];

      if (userId == null) {
        throw Exception('User not logged in');
      }

      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiBaseUrl}$favoritesEndpoint/user/$userId',
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      );

      final data = await ApiUtils.handleApiResponse(response);

      if (data['success'] == true && data['data'] != null) {
        // Return the full data array with post details
        return data['data'];
      }

      return [];
    } catch (e) {
      print('Error getting user favorites with details: $e');
      return [];
    }
  }

  // Helper function to toggle favorite status
  static Future<bool> toggleFavorite(Post post) async {
    try {
      if (post.isFavorite) {
        // If already favorite, remove it
        final result = await removeFromFavorites(post.id);
        return !result; // We want to return false since we're removing it
      } else {
        // If not favorite, add it
        final result = await addToFavorites(post.id);
        return result; // Return true if adding succeeded
      }
    } catch (e) {
      print('Error toggling favorite: $e');
      throw e; // Propagate error to be handled by UI
    }
  }
}