import React from 'react';
import { Link } from 'react-router-dom';

const Dashboard = () => {
  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">Tổ<PERSON> quan hệ thống</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Crawl Data</h2>
          <p className="text-gray-600 mb-4">Crawl dữ liệu từ các nguồn bên ngoài</p>
          <Link to="/crawl" className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 inline-block">
            Đi đến
          </Link>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Update Data</h2>
          <p className="text-gray-600 mb-4">Cậ<PERSON> nh<PERSON>t và xử lý dữ liệu đã crawl</p>
          <Link to="/update" className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 inline-block">
            Đi đến
          </Link>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Danh sách bài viết</h2>
          <p className="text-gray-600 mb-4">Xem tất cả các bài viết đã crawl</p>
          <Link to="/posts" className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 inline-block">
            Đi đến
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;