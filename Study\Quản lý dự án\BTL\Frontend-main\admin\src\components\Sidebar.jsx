import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Sidebar = () => {
  const location = useLocation();
  
  const isActive = (path) => {
    return location.pathname === path ? 'bg-indigo-700 text-white' : 'text-gray-300 hover:bg-indigo-600 hover:text-white';
  };

  return (
    <div className="bg-indigo-800 w-64 space-y-6 py-7 px-2 absolute inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition duration-200 ease-in-out">
      <div className="flex items-center justify-center">
        <div className="text-white text-2xl font-semibold">Admin Panel</div>
      </div>

      <nav>
        <Link to="/" className={`flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/')}`}>
          <span className="mx-4">Dashboard</span>
        </Link>
        <Link to="/crawl" className={`flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/crawl')}`}>
          <span className="mx-4">Crawl Data</span>
        </Link>
        <Link to="/update" className={`flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/update')}`}>
          <span className="mx-4">Update Data</span>
        </Link>
        <Link to="/posts" className={`flex items-center py-2.5 px-4 rounded transition duration-200 ${isActive('/posts')}`}>
          <span className="mx-4">Danh sách bài viết</span>
        </Link>
      </nav>
    </div>
  );
};

export default Sidebar;