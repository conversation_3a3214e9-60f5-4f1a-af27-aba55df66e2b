import 'package:flutter/material.dart';

class VideoItem {
  final String id;
  final String title;
  final String author;
  final String thumbnail;
  final String duration;

  VideoItem({
    required this.id,
    required this.title,
    required this.author,
    required this.thumbnail,
    required this.duration,
  });
}

class VlogScreen extends StatelessWidget {
  VlogScreen({Key? key}) : super(key: key);

  final List<VideoItem> videoData = [
    VideoItem(
        id: '1',
        title: '<PERSON><PERSON><PERSON><PERSON> - <PERSON>t kết nối | EP',
        author: '<PERSON><PERSON><PERSON><PERSON>',
        thumbnail: 'https://scontent.fhan18-1.fna.fbcdn.net/v/t39.30808-6/481207556_122149544102379626_8959497513831230955_n.jpg?_nc_cat=104&ccb=1-7&_nc_sid=aa7b47&_nc_eui2=AeGqWw8XNQmeQp1Qtd6azMZpnXoJ3RKDA1GdegndEoMDUUdi-HFJbbirPveXh4xn1cTo7qsW0WGRjAF04k6YpmI-&_nc_ohc=us1HjDxL-18Q7kNvgF8B4zg&_nc_oc=AdmzWO-81dA7kpdzaRCbSamEh02RFhdYKNs0Jb5VUsZGllAxW2xJ59OX1tQFTBPiqVU&_nc_zt=23&_nc_ht=scontent.fhan18-1.fna&_nc_gid=nlQ_83otKchbb6IjLvWwbA&oh=00_AYFqiOF3QersqIU5JXKGpYDEnNLLBTCw9uMST6RxOLPKyQ&oe=67E2AE61',
        duration: '3:28'
    ),
    VideoItem(
        id: '2',
        title: 'Dương Domic - Mắt kết nối | EP',
        author: 'Dương Domic',
        thumbnail: 'https://scontent.fhan18-1.fna.fbcdn.net/v/t39.30808-6/481207556_122149544102379626_8959497513831230955_n.jpg?_nc_cat=104&ccb=1-7&_nc_sid=aa7b47&_nc_eui2=AeGqWw8XNQmeQp1Qtd6azMZpnXoJ3RKDA1GdegndEoMDUUdi-HFJbbirPveXh4xn1cTo7qsW0WGRjAF04k6YpmI-&_nc_ohc=us1HjDxL-18Q7kNvgF8B4zg&_nc_oc=AdmzWO-81dA7kpdzaRCbSamEh02RFhdYKNs0Jb5VUsZGllAxW2xJ59OX1tQFTBPiqVU&_nc_zt=23&_nc_ht=scontent.fhan18-1.fna&_nc_gid=nlQ_83otKchbb6IjLvWwbA&oh=00_AYFqiOF3QersqIU5JXKGpYDEnNLLBTCw9uMST6RxOLPKyQ&oe=67E2AE61',
        duration: '3:28'
    ),
    VideoItem(
        id: '3',
        title: 'Dương Domic - Mắt kết nối | EP',
        author: 'Dương Domic',
        thumbnail: 'https://scontent.fhan18-1.fna.fbcdn.net/v/t39.30808-6/481207556_122149544102379626_8959497513831230955_n.jpg?_nc_cat=104&ccb=1-7&_nc_sid=aa7b47&_nc_eui2=AeGqWw8XNQmeQp1Qtd6azMZpnXoJ3RKDA1GdegndEoMDUUdi-HFJbbirPveXh4xn1cTo7qsW0WGRjAF04k6YpmI-&_nc_ohc=us1HjDxL-18Q7kNvgF8B4zg&_nc_oc=AdmzWO-81dA7kpdzaRCbSamEh02RFhdYKNs0Jb5VUsZGllAxW2xJ59OX1tQFTBPiqVU&_nc_zt=23&_nc_ht=scontent.fhan18-1.fna&_nc_gid=nlQ_83otKchbb6IjLvWwbA&oh=00_AYFqiOF3QersqIU5JXKGpYDEnNLLBTCw9uMST6RxOLPKyQ&oe=67E2AE61',
        duration: '3:28'
    ),
    VideoItem(
        id: '4',
        title: 'Dương Domic - Mắt kết nối | EP',
        author: 'Dương Domic',
        thumbnail: 'https://scontent.fhan18-1.fna.fbcdn.net/v/t39.30808-6/481207556_122149544102379626_8959497513831230955_n.jpg?_nc_cat=104&ccb=1-7&_nc_sid=aa7b47&_nc_eui2=AeGqWw8XNQmeQp1Qtd6azMZpnXoJ3RKDA1GdegndEoMDUUdi-HFJbbirPveXh4xn1cTo7qsW0WGRjAF04k6YpmI-&_nc_ohc=us1HjDxL-18Q7kNvgF8B4zg&_nc_oc=AdmzWO-81dA7kpdzaRCbSamEh02RFhdYKNs0Jb5VUsZGllAxW2xJ59OX1tQFTBPiqVU&_nc_zt=23&_nc_ht=scontent.fhan18-1.fna&_nc_gid=nlQ_83otKchbb6IjLvWwbA&oh=00_AYFqiOF3QersqIU5JXKGpYDEnNLLBTCw9uMST6RxOLPKyQ&oe=67E2AE61',
        duration: '3:28'
    ),
    VideoItem(
        id: '5',
        title: 'Dương Domic - Mắt kết nối | EP',
        author: 'Dương Domic',
        thumbnail: 'https://scontent.fhan18-1.fna.fbcdn.net/v/t39.30808-6/481207556_122149544102379626_8959497513831230955_n.jpg?_nc_cat=104&ccb=1-7&_nc_sid=aa7b47&_nc_eui2=AeGqWw8XNQmeQp1Qtd6azMZpnXoJ3RKDA1GdegndEoMDUUdi-HFJbbirPveXh4xn1cTo7qsW0WGRjAF04k6YpmI-&_nc_ohc=us1HjDxL-18Q7kNvgF8B4zg&_nc_oc=AdmzWO-81dA7kpdzaRCbSamEh02RFhdYKNs0Jb5VUsZGllAxW2xJ59OX1tQFTBPiqVU&_nc_zt=23&_nc_ht=scontent.fhan18-1.fna&_nc_gid=nlQ_83otKchbb6IjLvWwbA&oh=00_AYFqiOF3QersqIU5JXKGpYDEnNLLBTCw9uMST6RxOLPKyQ&oe=67E2AE61',
        duration: '3:28'
    ),
  ];

  Widget _buildVideoItem(VideoItem item) {
    return GestureDetector(
      onTap: () {},
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: Image.network(
                  item.thumbnail,
                  width: 120,
                  height: 68,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 120,
                      height: 68,
                      color: const Color(0xFFF0F0F0),
                    );
                  },
                ),
              ),
              Positioned(
                right: 4,
                bottom: 4,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Text(
                    item.duration,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                  ),
                ),
              ),
            ],
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    item.title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.author,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                ),
              ),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: const Padding(
                      padding: EdgeInsets.only(right: 16),
                      child: Icon(Icons.arrow_back, size: 24),
                    ),
                  ),
                  const Text(
                    'Vlog review',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // Search bar
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    child: Icon(Icons.search, size: 20, color: Colors.grey),
                  ),
                  Expanded(
                    child: TextField(
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                      ),
                      decoration: const InputDecoration(
                        hintText: "Search",
                        hintStyle: TextStyle(color: Colors.grey),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Video list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: videoData.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _buildVideoItem(videoData[index]),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}