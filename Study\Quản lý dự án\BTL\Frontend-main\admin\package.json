{"name": "admin", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.1", "lucide-react": "^0.511.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.2.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:css": "tailwindcss -i ./src/input.css -o ./src/index.css --watch"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.20", "postcss": "^8.5.3", "tailwindcss": "^3.4.1"}}