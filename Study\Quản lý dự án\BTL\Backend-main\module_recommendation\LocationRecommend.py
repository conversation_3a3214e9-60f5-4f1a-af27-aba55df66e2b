import pandas as pd
import math

class LocationRecommender:
    
    def __init__(self, posts_df):
        
        self.posts_df = posts_df
    
    def _haversine_distance(self, lat1, lon1, lat2, lon2):
        # Convert latitude and longitude from degrees to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

        # Haversine formula
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371  # Radius of the Earth in kilometers
        return c * r
    
    def get_recommendations(self, user_lat, user_lon, max_distance_km=10):
        # Check if user coordinates are valid
        if pd.isna(user_lat) or pd.isna(user_lon):
            return pd.DataFrame(columns=['post_id', 'distance_km'])

        # Calculate distance for each post
        result = []
        for _, post in self.posts_df.iterrows():
            if pd.isna(post['latitude']) or pd.isna(post['longitude']):
                continue
                
            distance = self._haversine_distance(
                user_lat, user_lon, 
                post['latitude'], post['longitude']
            )
            
            if distance <= max_distance_km:
                result.append({
                    'post_id': post['id'],
                    'distance_km': distance
                })

        # Sort by distance
        recommendations = pd.DataFrame(result)
        if recommendations.empty:
            return recommendations
            
        return recommendations.sort_values('distance_km')
    
    def __init__(self, posts_df):
        self.posts_df = posts_df
    
    def _haversine_distance(self, lat1, lon1, lat2, lon2):

        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371
        return c * r
    
    def get_recommendations(self, user_lat, user_lon, max_distance_km):

        if pd.isna(user_lat) or pd.isna(user_lon):
            return pd.DataFrame(columns=['post_id', 'distance_km'])

        result = []
        for _, post in self.posts_df.iterrows():
            if pd.isna(post['latitude']) or pd.isna(post['longitude']):
                continue
                
            distance = self._haversine_distance(
                user_lat, user_lon, 
                post['latitude'], post['longitude']
            )
            
            if distance <= max_distance_km:
                result.append({
                    'post_id': post['id'],
                    'distance_km': distance
                })

        recommendations = pd.DataFrame(result)
        if recommendations.empty:
            return recommendations
            
        return recommendations.sort_values('distance_km')