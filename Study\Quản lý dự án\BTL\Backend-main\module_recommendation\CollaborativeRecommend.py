import pandas as pd
import numpy as np
from scipy.sparse.linalg import svds

class CollaborativeRecommender:
    
    def __init__(self, user_interactions):
        self.user_interactions = user_interactions
        self.interactions_matrix = self._create_interaction_matrix()
    
    def _create_interaction_matrix(self):
        if self.user_interactions.empty:
            return pd.DataFrame()
        interaction_matrix = self.user_interactions.pivot(
            index='user_id', 
            columns='post_id', 
            values='interaction_strength'
        ).fillna(0)
        return interaction_matrix
    
    def _matrix_factorization(self, k=20):
        if self.interactions_matrix.empty:
            return None, None, None
        interactions_matrix = self.interactions_matrix.values
        user_ratings_mean = np.mean(interactions_matrix, axis=1).reshape(-1, 1)
        normalized_matrix = interactions_matrix - user_ratings_mean
        min_dim = min(normalized_matrix.shape)
        if min_dim <= 1:
            return None, None, None
        k = min(k, min_dim - 1)
        u, sigma, vt = svds(normalized_matrix, k=k)
        sigma_diag = np.diag(sigma)
        user_features = u @ sigma_diag
        post_features = vt.T
        return user_features, post_features, user_ratings_mean
    
    def get_recommendations(self, user_id):
        if self.interactions_matrix.empty:
            return pd.DataFrame(columns=['post_id', 'predicted_score'])
        if user_id not in self.interactions_matrix.index:
            return pd.DataFrame(columns=['post_id', 'predicted_score'])
        user_idx = self.interactions_matrix.index.get_loc(user_id)
        user_features, post_features, user_ratings_mean = self._matrix_factorization()
        if user_features is None:
            return pd.DataFrame(columns=['post_id', 'predicted_score'])
        user_pred_base = float(user_ratings_mean[user_idx][0])
        user_predictions = user_pred_base + (user_features[user_idx] @ post_features.T)
        user_interactions = self.interactions_matrix.iloc[user_idx]
        user_interacted_posts = user_interactions[user_interactions > 0].index.tolist()
        post_ids = self.interactions_matrix.columns.tolist()
        result = []
        for i, post_id in enumerate(post_ids):
            if post_id not in user_interacted_posts:
                result.append({
                    'post_id': post_id,
                    'predicted_score': float(user_predictions[i])
                })
        recommendations = pd.DataFrame(result)
        if recommendations.empty:
            return recommendations
        return recommendations.sort_values('predicted_score', ascending=False)
