import 'package:flutter/material.dart';
import '../constants/colors.dart';
import '../constants/images.dart';
import '../screens/search_screen.dart';

class SearchBarPost extends StatelessWidget {
  const SearchBarPost({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      color: AppColors.redPrimary,
      child: InkWell(
        onTap: () {
          // Navigate to search screen when tapped
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const SearchScreen()),
          );
        },
        child: Container(
          height: 40,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(5),
          ),
          child: Row(
            children: [
              Image.asset(
                AppImages.search,
                width: 20,
                height: 20,
                color: Gray.text,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'What do you think?',
                  style: TextStyle(
                    color: Gray.text,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}