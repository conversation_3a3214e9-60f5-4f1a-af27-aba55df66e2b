import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import '../constants/colors.dart';
import '../constants/images.dart';

class CategoryBar extends StatefulWidget {
  final Function(String) onCategoryTap;

  const CategoryBar({
    Key? key,
    required this.onCategoryTap,
  }) : super(key: key);

  @override
  CategoryBarState createState() => CategoryBarState();
}

class CategoryBarState extends State<CategoryBar> {
  // Category visibility state
  bool isExpandedCategories = true;
  double categoryHeight = 80.0;

  // Map category names to their search queries
  final Map<String, String> categorySearchQueries = {
    'Restaurant': 'nhà hàng, quán ăn, nhà ăn, restaurant',
    'Cafe': 'cafe, quán cafe, quán cà phê, coffee shop',
    'Shopping': 'shopping, trung tâm mua sắm, shop, cửa hàng',
    'Hotel': 'khách sạn, hotel, nhà nghỉ, chỗ ở',
    'Cinema': 'rạp chiếu phim, cinema, phim, movie theater',
  };

  void updateExpansion(ScrollDirection direction) {
    if (direction == ScrollDirection.reverse) {
      // Scrolling down - collapse categories
      if (isExpandedCategories) {
        setState(() {
          isExpandedCategories = false;
          categoryHeight = 55.0; // Just enough for the icon
        });
      }
    } else if (direction == ScrollDirection.forward) {
      // Scrolling up - expand categories
      if (!isExpandedCategories) {
        setState(() {
          isExpandedCategories = true;
          categoryHeight = 80.0; // Enough for icon + text
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      height: categoryHeight,
      margin: const EdgeInsets.symmetric(vertical: 5),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Row(
          children: [
            buildCategoryItem(AppImages.restaurant, 'Restaurant'),
            const SizedBox(width: 24),
            buildCategoryItem(AppImages.cafe, 'Cafe'),
            const SizedBox(width: 24),
            buildCategoryItem(AppImages.shopping, 'Shopping'),
            const SizedBox(width: 24),
            buildCategoryItem(AppImages.hotel, 'Hotel'),
            const SizedBox(width: 24),
            buildCategoryItem(AppImages.cinema, 'Cinema'),
          ],
        ),
      ),
    );
  }

  Widget buildCategoryItem(String iconAsset, String label) {
    return GestureDetector(
      onTap: () {
        // When category is tapped, pass the search query to the parent
        widget.onCategoryTap(categorySearchQueries[label] ?? label);
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon - keep its size consistent
          SizedBox(
            width: 40,
            height: 40,
            child: Image.asset(
              iconAsset,
              width: 40,
              height: 40,
              fit: BoxFit.contain,
            ),
          ),
          // Animated container for the text that can collapse to zero height
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: isExpandedCategories ? 15 : 0,
            child: isExpandedCategories
                ? Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: AppColors.black,
              ),
              textAlign: TextAlign.center,
            )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}