import mysql.connector
from datetime import datetime, timedelta

class DatabaseManager:
    def __init__(self, mysql_config):
        self.mysql_config = mysql_config
        self.connection = self.create_mysql_connection()
        
    def create_mysql_connection(self):
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            print("Connection successful")
            return connection
        except mysql.connector.Error as err:
            print(f"Error connecting to MySQL: {err}")
            return None
    def execute_query(self, query, params=None, fetch_one=False, fetch_all=False):

        try:
            cursor = self.connection.cursor(dictionary=True)
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch_one:
                result = cursor.fetchone()
            elif fetch_all:
                result = cursor.fetchall()
            else:
                result = None
            
            cursor.close()
            return result
        
        except mysql.connector.Error as err:
            print(f"Database query error: {err}")
            return None