import 'package:flutter/foundation.dart';
import '../api/auth.dart';
import '../api/profile.dart';
import '../api/token.dart';
import '../utils/auth_utils.dart';
import '../utils/user_preferences_utils.dart'; // Import the new utility
import 'dart:convert';

class AuthProvider with ChangeNotifier {
  Map<String, dynamic>? _userData;
  bool _isLoading = false;
  String? _error;
  bool _isAuthenticated = false;
  bool _isRefreshing = false;
  bool _isProfileComplete = false;

  // Getters
  Map<String, dynamic>? get userData => _userData;

  bool get isLoading => _isLoading;

  String? get error => _error;

  bool get isAuthenticated => _isAuthenticated;

  bool get isRefreshing => _isRefreshing;

  bool get isProfileComplete => _isProfileComplete;

  AuthProvider() {
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Check if we have stored tokens
      final token = await AuthUtils.getAuthToken();
      final storedRefreshToken = await AuthUtils.getRefreshToken();
      final userData = await AuthUtils.getUserData();

      if (token != null && storedRefreshToken != null && userData != null) {
        final isTokenValid = await _validateToken(token);

        if (isTokenValid) {
          _userData = userData;
          _isAuthenticated = true;

          await _syncUserProfileData();
        } else {
          final isRefreshed = await refreshToken();

          if (isRefreshed) {
            await _syncUserProfileData();
          } else {
            await logout(silent: true);
          }
        }
      }
    } catch (e) {
      print('Auth initialization error: $e');
      await logout(silent: true);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _syncUserProfileData() async {
    try {
      // Check if we need to fetch profile from server
      final isProfileComplete =
          await UserPreferencesUtils.isUserProfileComplete();

      if (!isProfileComplete) {
        final profileData = await getProfile();

        if (profileData != null) {
          await _saveUserProfileToPreferences(profileData);
        }
      } else {
        final localProfile = await UserPreferencesUtils.getUserProfile();
        if (localProfile['id'] != null) {
          _isProfileComplete = true;
        }
      }
    } catch (e) {
      print('Profile sync error: $e');
      await getProfile();
    }
  }

  Future<void> _saveUserProfileToPreferences(
      Map<String, dynamic> profile) async {
    print(profile);
    try {
      await UserPreferencesUtils.storeUserProfile(
        id: profile['_id'] != null ? profile['_id'].toString() : '',
        name: profile['username'] ?? '',
        email: profile['email'] ?? '',
        address: profile['address'],
        dob: profile['dob'],
        searchDistance: profile['distance'] != null
            ? double.tryParse(profile['distance'].toString())
            : null,
      );

      _isProfileComplete = true;
    } catch (e) {
      print('Error saving user profile data: $e');
    }
  }

  Future<bool> _validateToken(String token) async {
    try {
      final response = await ProfileApi.getProfile();
      return response['success'] == true;
    } catch (e) {
      print('Token validation error: $e');
      return false;
    }
  }

  Future<bool> login(String email, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await AuthApi.login(email, password);

      if (response.containsKey('data') &&
          response['data'] is Map<String, dynamic> &&
          response['data'].containsKey('accessToken') &&
          response['data'].containsKey('refreshToken')) {
        final token = response['data']['accessToken'] as String;
        final refreshToken = response['data']['refreshToken'] as String;
        final userData = response['data'] as Map<String, dynamic>;

        await AuthUtils.storeAuthData(token, refreshToken, userData);
        _userData = userData;
        _isAuthenticated = true;

        print(userData);
        // Save user profile data to preferences
        await _saveUserProfileToPreferences(userData);

        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = 'Invalid response from server';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> register(String nameAccount, String email, String dateOfBirth,
      String address, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await AuthApi.register(
          nameAccount, email, dateOfBirth, password, address);

      if (response.containsKey('data') &&
          response['data'] is Map<String, dynamic> &&
          response['data'].containsKey('accessToken') &&
          response['data'].containsKey('refreshToken')) {
        final token = response['data']['accessToken'] as String;
        final refreshToken = response['data']['refreshToken'] as String;
        final userData = response['data'] as Map<String, dynamic>;

        await AuthUtils.storeAuthData(token, refreshToken, userData);
        _userData = userData;
        _isAuthenticated = true;

        // Save user profile data to preferences
        await _saveUserProfileToPreferences(userData);

        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = 'Invalid response from server';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> googleSignIn(String googleToken) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await AuthApi.googleSignIn(googleToken);

      if (response.containsKey('data') &&
          response['data'].containsKey('accessToken') &&
          response['data'].containsKey('refreshToken') &&
          response['data'].containsKey('user')) {
        final token = response['data']['accessToken'] as String;
        final refreshToken = response['data']['refreshToken'] as String;
        final userData = response['data']['user'] as Map<String, dynamic>;

        await AuthUtils.storeAuthData(token, refreshToken, userData);
        _userData = userData;
        _isAuthenticated = true;

        // Save user profile data to preferences
        await _saveUserProfileToPreferences(userData);

        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = 'Invalid response from server';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> refreshToken() async {
    if (_isRefreshing) return false;

    _isRefreshing = true;
    notifyListeners();

    try {
      final oldRefreshToken = await AuthUtils.getRefreshToken();
      final userId = _userData?['_id'] ?? _userData?['id'];

      if (oldRefreshToken == null || userId == null) {
        // Cannot refresh without a refresh token or user ID
        await logout(silent: true);
        return false;
      }

      final response = await TokenApi.refreshToken(userId, oldRefreshToken);

      if (response.containsKey('success') &&
          response['success'] == true &&
          response.containsKey('accessToken') &&
          response.containsKey('refreshToken')) {
        final newAccessToken = response['accessToken'] as String;
        final newRefreshToken = response['refreshToken'] as String;

        // Update tokens in local storage
        await AuthUtils.updateAuthToken(newAccessToken);
        await AuthUtils.updateRefreshToken(newRefreshToken);

        // Update user data if available
        if (response.containsKey('user')) {
          _userData = response['user'];
          await AuthUtils.updateUserData(_userData!);

          // Update local user profile data
          await _saveUserProfileToPreferences(_userData!);
        }

        _isRefreshing = false;
        notifyListeners();
        return true;
      } else {
        await logout(silent: true);
        _isRefreshing = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      print('Token refresh error in provider: $e');
      await logout(silent: true);
      _isRefreshing = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> logout({bool silent = false}) async {
    if (!silent) {
      _isLoading = true;
      notifyListeners();
    }

    try {
      try {
        await AuthApi.logout();
      } catch (e) {
        print('Logout API call failed, but proceeding with local logout: $e');
      }

      await AuthUtils.clearAuthData();
      await UserPreferencesUtils.clearUserProfile();

      _userData = null;
      _isAuthenticated = false;
      _isProfileComplete = false;
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      await AuthUtils.clearAuthData();
      await UserPreferencesUtils.clearUserProfile();
      _userData = null;
      _isAuthenticated = false;
      _isProfileComplete = false;
      _isLoading = false;
      notifyListeners();
      return true;
    }
  }

  Future<Map<String, dynamic>?> getProfile() async {
    try {
      final response = await ProfileApi.getProfile();

      if (response['success']) {
        final profileData = response['data'];
        _userData = {
          'id': profileData['_id'],
          'username': profileData['username'],
          'email': profileData['email'],
          'dateOfBirth': profileData['dateOfBirth'],
          'address': profileData['address'],
          'distance': profileData['search_distance'],
        };

        // Save to preferences
        await _saveUserProfileToPreferences(_userData!);

        notifyListeners();
        return _userData;
      }
      return {};
    } catch (error) {
      print('Get profile error in provider: $error');
      if (error.toString().contains('token failed') ||
          error.toString().contains('Not authorized')) {
        // Try to refresh the token
        final refreshed = await refreshToken();
        if (refreshed) {
          return getProfile();
        }
      }

      throw error;
    }
  }

  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await ProfileApi.updateProfile(profileData);

      if (response['success']) {
        final updatedData = response['data'];

        _userData = {
          'id': updatedData['_id'],
          'username': updatedData['username'],
          'email': updatedData['email'],
          'dateOfBirth': updatedData['dateOfBirth'],
          'address': updatedData['address'],
          'search_distance': updatedData['search_distance'],
        };

        final token = await AuthUtils.getAuthToken();
        final refreshToken = await AuthUtils.getRefreshToken();
        if (token != null && refreshToken != null) {
          await AuthUtils.storeAuthData(token, refreshToken, _userData!);
        }

        // Update local profile data
        await _saveUserProfileToPreferences(_userData!);

        notifyListeners();
        return true;
      }
      return false;
    } catch (error) {
      print('Update profile error in provider: $error');

      // Check if error is due to token expiration
      if (error.toString().contains('token failed') ||
          error.toString().contains('Not authorized')) {
        // Try to refresh the token
        final refreshed = await refreshToken();
        if (refreshed) {
          // Retry the request if token refresh was successful
          _isLoading = false;
          notifyListeners();
          return updateProfile(profileData);
        }
      }

      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // New method to update search distance preference
  Future<bool> updateSearchDistance(double distance) async {
    try {
      await UserPreferencesUtils.updateSearchDistance(distance);

      // If user is authenticated, also update on server
      if (_isAuthenticated) {
        return await updateProfile({'search_distance': distance});
      }

      return true;
    } catch (e) {
      print('Error updating search distance: $e');
      return false;
    }
  }
}
