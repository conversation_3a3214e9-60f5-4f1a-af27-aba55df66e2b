import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class UserPreferencesUtils {
  // User data keys
  static const String USER_ID_KEY = 'user_id';
  static const String USER_NAME_KEY = 'user_name';
  static const String USER_EMAIL_KEY = 'user_email';
  static const String USER_ADDRESS_KEY = 'user_address';
  static const String USER_DOB_KEY = 'user_dob'; // Added DOB key with proper naming convention
  static const String USER_LAT_KEY = 'user_latitude';
  static const String USER_LON_KEY = 'user_longitude';
  static const String USER_SEARCH_DISTANCE_KEY = 'user_search_distance';
  static const String USER_PROFILE_COMPLETE_KEY = 'user_profile_complete';

  // Default values
  static const double DEFAULT_SEARCH_DISTANCE = 1.0;

  // Store complete user profile data
  static Future<void> storeUserProfile({
    required String id,
    required String name,
    required String email,
    String? address,
    String? dob, // Added date of birth parameter
    double? latitude,
    double? longitude,
    double? searchDistance,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(USER_ID_KEY, id);
    await prefs.setString(USER_NAME_KEY, name);
    await prefs.setString(USER_EMAIL_KEY, email);

    if (address != null) await prefs.setString(USER_ADDRESS_KEY, address);
    if (dob != null) await prefs.setString(USER_DOB_KEY, dob); // Store DOB if provided
    if (latitude != null) await prefs.setDouble(USER_LAT_KEY, latitude);
    if (longitude != null) await prefs.setDouble(USER_LON_KEY, longitude);
    if (searchDistance != null)
      await prefs.setDouble(USER_SEARCH_DISTANCE_KEY, searchDistance);

    // Mark profile as complete if we have all essential data
    final isComplete = id.isNotEmpty &&
        name.isNotEmpty &&
        email.isNotEmpty &&
        (address?.isNotEmpty ?? false) &&
        (dob?.isNotEmpty ?? false); // Added DOB to completeness check

    await prefs.setBool(USER_PROFILE_COMPLETE_KEY, isComplete);
  }

  // Update a specific user profile field
  static Future<void> updateUserProfileField(String key, dynamic value) async {
    final prefs = await SharedPreferences.getInstance();

    if (value is String) {
      await prefs.setString(key, value);
    } else if (value is double) {
      await prefs.setDouble(key, value);
    } else if (value is int) {
      await prefs.setInt(key, value);
    } else if (value is bool) {
      await prefs.setBool(key, value);
    }
  }

  // Get user profile data as a map
  static Future<Map<String, dynamic>> getUserProfile() async {
    final prefs = await SharedPreferences.getInstance();

    return {
      'id': prefs.getString(USER_ID_KEY),
      'name': prefs.getString(USER_NAME_KEY),
      'email': prefs.getString(USER_EMAIL_KEY),
      'address': prefs.getString(USER_ADDRESS_KEY),
      'dob': prefs.getString(USER_DOB_KEY), // Added DOB to the returned map
      'latitude': prefs.getDouble(USER_LAT_KEY),
      'longitude': prefs.getDouble(USER_LON_KEY),
      'searchDistance':
      prefs.getDouble(USER_SEARCH_DISTANCE_KEY) ?? DEFAULT_SEARCH_DISTANCE,
      'isProfileComplete': prefs.getBool(USER_PROFILE_COMPLETE_KEY) ?? false,
    };
  }

  // Check if user profile has all necessary data
  static Future<bool> isUserProfileComplete() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(USER_PROFILE_COMPLETE_KEY) ?? false;
  }

  // Clear user profile data
  static Future<void> clearUserProfile() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(USER_ID_KEY);
    await prefs.remove(USER_NAME_KEY);
    await prefs.remove(USER_EMAIL_KEY);
    await prefs.remove(USER_ADDRESS_KEY);
    await prefs.remove(USER_DOB_KEY); // Added DOB to be cleared as well
    await prefs.remove(USER_LAT_KEY);
    await prefs.remove(USER_LON_KEY);
    await prefs.remove(USER_SEARCH_DISTANCE_KEY);
    await prefs.remove(USER_PROFILE_COMPLETE_KEY);
  }

  // Get search distance preference
  static Future<double> getSearchDistance() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(USER_SEARCH_DISTANCE_KEY) ?? DEFAULT_SEARCH_DISTANCE;
  }

  // Update search distance preference
  static Future<void> updateSearchDistance(double distance) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(USER_SEARCH_DISTANCE_KEY, distance);
  }

  // Get date of birth
  static Future<String?> getDateOfBirth() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(USER_DOB_KEY);
  }

  // Update date of birth
  static Future<void> updateDateOfBirth(String dob) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(USER_DOB_KEY, dob);

    // Update profile completeness status after updating DOB
    final id = prefs.getString(USER_ID_KEY) ?? '';
    final name = prefs.getString(USER_NAME_KEY) ?? '';
    final email = prefs.getString(USER_EMAIL_KEY) ?? '';
    final address = prefs.getString(USER_ADDRESS_KEY) ?? '';

    final isComplete = id.isNotEmpty &&
        name.isNotEmpty &&
        email.isNotEmpty &&
        address.isNotEmpty &&
        dob.isNotEmpty;

    await prefs.setBool(USER_PROFILE_COMPLETE_KEY, isComplete);
  }
}