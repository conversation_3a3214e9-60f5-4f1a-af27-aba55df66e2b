# -*- coding: utf-8 -*-
from flask import Flask, Blueprint, jsonify
from predict import extract_address_rule_based, get_lat_long
import os
import time
from dotenv import load_dotenv
from config import get_mysql_config
from database import DatabaseManager
import re
from flask_cors import CORS 
load_dotenv()

app = Flask(__name__)
CORS(app) 
MYSQL_CONFIG = get_mysql_config()
db_manager = DatabaseManager(MYSQL_CONFIG)

api_v1 = Blueprint('api/v1', __name__, url_prefix='/api/v1')

def get_lat_long_with_delay(address, delay=1):
    time.sleep(delay) 
    return get_lat_long(address)

@api_v1.route('/tag-position', methods=['GET'])
def predict():
    try:
        # Chỉ lấy các bản ghi có isTaggedPosition = 0
        posts = db_manager.execute_query("SELECT id, message FROM posts WHERE isTaggedPosition = 0", fetch_all=True)
        if not posts:
            return jsonify({"error": "Không tìm thấy bài viết nào cần cập nhật"}), 404

        results = []
        for post in posts:
            post_id = post['id']
            message = post['message']

            address_result = extract_address_rule_based(message)
            
            # Check if there was an error in address extraction
            if isinstance(address_result, dict) and "error" in address_result:
                print(f"Post {post_id}: {address_result['error']}")
                continue
            
            if not address_result.get('addresses'):
                print(f"Post {post_id}: Không tìm thấy địa chỉ")
                continue
            
            # Get geographical coordinates with delay to avoid rate limiting
            lat_lng = get_lat_long_with_delay(address_result['addresses'][0], delay=1)
            
            # Properly check if lat_lng is valid and has the expected structure
            if not lat_lng or not isinstance(lat_lng, list) or len(lat_lng) == 0:
                print(f"Post {post_id}: Không nhận được dữ liệu vị trí từ API")
                continue
                
            if 'locations' not in lat_lng[0] or not lat_lng[0]['locations']:
                print(f"Post {post_id}: Không có locations trong dữ liệu vị trí")
                continue
                
            # Extract latitude and longitude
            lat = lat_lng[0]['locations'][0]['latLng']['lat']
            lng = lat_lng[0]['locations'][0]['latLng']['lng']
            print(f"Kết quả cho post {post_id}: {address_result['addresses'][0]} -> {lat}, {lng}")
            
            # Update database with the new information
            update_query = """
                UPDATE posts SET address = %s, latitude = %s, longitude = %s, isTaggedPosition = 1 WHERE id = %s
            """
            db_manager.execute_query(update_query, (address_result['addresses'][0], lat, lng, post_id))
            
            # Add to results
            results.append({
                "post_id": post_id,
                "address": address_result['addresses'][0],
                "latitude": lat,
                "longitude": lng
            })
        
        return jsonify({"updated_posts": results})
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return jsonify({"error": str(e)}), 500


app.register_blueprint(api_v1)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5015, debug=True)