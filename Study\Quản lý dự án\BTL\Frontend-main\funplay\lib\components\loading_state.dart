// lib/widgets/post_detail/loading_state.dart
import 'package:flutter/material.dart';

class LoadingState extends StatelessWidget {
  final VoidCallback onBack;

  const LoadingState({Key? key, required this.onBack}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Loading...'),
        leading: I<PERSON><PERSON><PERSON><PERSON>(
          icon: Icon(Icons.arrow_back),
          onPressed: onBack,
        ),
      ),
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
