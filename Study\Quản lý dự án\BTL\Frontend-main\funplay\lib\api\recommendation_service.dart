import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/post.dart';
import 'config.dart';
import '../utils/auth_request_utils.dart';
import '../utils/auth_utils.dart';
import '../utils/user_preferences_utils.dart';
import '../utils/api_utils.dart';
import 'favorite_service.dart';

class RecommendationService {
  static const int _timeout = 15;
  static const int _pageSize = 10;

  static const String DISTANCE_PREF_KEY = 'user_distance_preference';
  static const double DEFAULT_DISTANCE = 1.0;

  static Future<double> getUserDistancePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // Try to get distance from SharedPreferences
      final distance = prefs.getDouble(DISTANCE_PREF_KEY);

      // If distance doesn't exist in SharedPreferences
      if (distance == null) {
        // Try to get user profile data to check if distance is stored there
        try {
          final userData = await AuthUtils.getUserData();
          if (userData != null && userData['distance_preference'] != null) {
            final userDistance =
                double.tryParse(userData['distance_preference'].toString()) ??
                    DEFAULT_DISTANCE;
            await prefs.setDouble(DISTANCE_PREF_KEY, userDistance);
            return userDistance;
          }
        } catch (e) {
          print("Error fetching user data for distance preference: $e");
        }

        // If we still don't have a distance, save and return the default
        await prefs.setDouble(DISTANCE_PREF_KEY, DEFAULT_DISTANCE);
        return DEFAULT_DISTANCE;
      }

      return distance;
    } catch (e) {
      print("Error getting user distance preference: $e");
      return DEFAULT_DISTANCE;
    }
  }

  // Save user distance preference to SharedPreferences
  static Future<void> saveUserDistancePreference(double distance) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(DISTANCE_PREF_KEY, distance);
    } catch (e) {
      print("Error saving user distance preference: $e");
    }
  }

  static Future<String> getUserId() async {
    try {
      final userProfile = await UserPreferencesUtils.getUserProfile();
      if (userProfile['id'] != null) {
        return userProfile['id'].toString();
      }

      // Fallback to AuthUtils if not found in UserPreferencesUtils
      final userData = await AuthUtils.getUserData();
      if (userData != null && userData['id'] != null) {
        return userData['id'].toString();
      }
    } catch (e) {
      print("Error getting user ID: $e");
    }
    return "1"; // Fallback to default if user ID can't be retrieved
  }

  static Future<List<Post>> fetchRecommendations({
    String? userId,
    required double lat,
    required double lon,
    required String method,
    int page = 1,
    int limit = 10,
    int retryAttempts = 2,
  }) async {
    try {
      final String uid = userId ?? await getUserId();

      // Get distance preference from UserPreferencesUtils instead
      final double distance = await UserPreferencesUtils.getSearchDistance();

      // Build query with pagination parameters
      final query = "?user_id=$uid&lat=$lat&lon=$lon&distance_km=$distance"
          "&method=$method&page=$page&limit=$limit";

      final List<Post> posts = await _fetchWithRetry(
        query: query,
        method: method,
        retryAttempts: retryAttempts,
      );

      return await _markFavorites(posts);
    } catch (e) {
      print("Error fetching recommendations ($method): $e");
      final dummyPosts = _getDummyData(method);
      return await _markFavorites(dummyPosts);
    }
  }

  static Future<List<Post>> _fetchWithRetry({
    required String query,
    required String method,
    int retryAttempts = 2,
  }) async {
    Exception? lastException;

    for (int attempt = 0; attempt <= retryAttempts; attempt++) {
      try {
        // Add backoff delay for retries
        if (attempt > 0) {
          await Future.delayed(Duration(milliseconds: 500 * attempt));
          print("Retry attempt $attempt for $method");
        }

        // Add timeout to prevent hanging on connection issues
        final Map<String, dynamic> response = await AuthRequestUtils.authFetch
            .getRecommend(query)
            .timeout(Duration(seconds: _timeout), onTimeout: () {
          throw TimeoutException("Request timed out after $_timeout seconds");
        });

        if (response['data'] != null && response['data'] is List) {
          print("Successfully fetched ${(response['data'] as List).length} $method recommendations");
          return (response['data'] as List)
              .where((item) => item != null) // Filter out null items
              .map((item) => Post.fromJson(item))
              .toList();
        }

        print("Empty response data for $method");
        return [];
      } catch (e) {
        print("Attempt $attempt failed: $e");
        print("Request URL: ${ApiConfig.apiBaseUrl}/recommend$query");

        lastException = e is Exception ? e : Exception("Unexpected error: '$e'");

        // Only continue if we have more attempts
        if (attempt == retryAttempts) {
          print("All retry attempts failed for $method: $lastException");
          break;
        }
      }
    }

    // If all attempts fail, throw the last exception
    throw lastException ?? Exception("Unknown error");
  }

  // Return fallback data when API fails
  static List<Post> _getDummyData(String method) {
    // Different dummy data based on recommendation type
    if (method == "location") {
      return [
        Post.dummyNearby(1),
        Post.dummyNearby(2),
        Post.dummyNearby(3),
      ];
    } else if (method == "popularity") {
      return [
        Post.dummyTrending(1),
        Post.dummyTrending(2),
      ];
    } else {
      return [
        Post.dummyPersonalized(1),
        Post.dummyPersonalized(2),
        Post.dummyPersonalized(3),
      ];
    }
  }

  // Mark favorites for a list of posts
  static Future<List<Post>> _markFavorites(List<Post> posts) async {
    try {
      // Get all user favorites
      final List<String> favorites = await FavoriteService.getUserFavorites();
      final Set<String> favoriteIds = Set<String>.from(favorites);

      // Mark posts that are in favorites
      for (Post post in posts) {
        post.isFavorite = favoriteIds.contains(post.id);
      }

      return posts;
    } catch (e) {
      // If there's an error getting favorites, return posts as is
      print("Error marking favorites: $e");
      return posts;
    }
  }

  // Fetch nearby recommendations with pagination
  static Future<List<Post>> fetchNearbyRecommendations({
    String? userId,
    required double lat,
    required double lon,
    int page = 1,
    int limit = 10,
  }) async {
    return fetchRecommendations(
      userId: userId,
      lat: lat,
      lon: lon,
      method: "location",
      page: page,
      limit: limit,
    );
  }

  // Fetch trending recommendations with pagination
  static Future<List<Post>> fetchTrendingRecommendations({
    String? userId,
    required double lat,
    required double lon,
    double? distanceKm,
    int page = 1,
    int limit = 10,
  }) async {
    // If userId or distanceKm are not provided, they will be retrieved from SharedPreferences
    return fetchRecommendations(
      userId: userId,
      lat: lat,
      lon: lon,
      method: "popularity",
      page: page,
      limit: limit,
    );
  }

  // Fetch personalized recommendations with pagination
  static Future<List<Post>> fetchPersonalizedRecommendations({
    String? userId,
    required double lat,
    required double lon,
    double? distanceKm,
    int page = 1,
    int limit = 10,
  }) async {
    return fetchRecommendations(
      userId: userId,
      lat: lat,
      lon: lon,
      method: "collaborative",
      page: page,
      limit: limit,
    );
  }

  // Fetch hybrid recommendations with pagination
  static Future<List<Post>> fetchHybridRecommendations({
    String? userId,
    required double lat,
    required double lon,
    double? distanceKm,
    int page = 1,
    int limit = 10,
  }) async {
    return fetchRecommendations(
      userId: userId,
      lat: lat,
      lon: lon,
      method: "hybrid",
      page: page,
      limit: limit,
    );
  }
}