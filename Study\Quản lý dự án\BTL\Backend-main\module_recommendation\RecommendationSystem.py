import pandas as pd

from database import DatabaseManager
from LocationRecommend import <PERSON><PERSON><PERSON>ommender
from ContentRecommend import ContentRecommender
from CollaborativeRecommend import CollaborativeRecommender
from PopularityRecommend import PopularityRecommender
from HybridRecommend import HybridRecommender

class RecommendationSystem:
    
    def __init__(self, db_connection):
        """
        Initialize recommendation system
        
        Parameters:
        -----------
        db_connection : database connection
            Connection to the database
        """
        self.db_manager = DatabaseManager(db_connection)
        
        # Load data
        self.posts_df = None
        self.comments_df = None
        self.sub_comments_df = None
        self.users_df = None
        self.user_favorites_df = None
        self.user_interactions = None
        
        # Initialize recommenders
        self.location_recommender = None
        self.content_recommender = None
        self.collaborative_recommender = None
        self.popularity_recommender = None
        self.hybrid_recommender = None
        
    def load_data(self):
        self.posts_df = self.db_manager.load_posts()
        self.comments_df = self.db_manager.load_comments()
        self.sub_comments_df = self.db_manager.load_sub_comments()
        self.users_df = self.db_manager.load_users()
        self.user_favorites_df = self.db_manager.load_user_favorites()
        self.user_interactions = self.db_manager.load_user_interactions()

        self.location_recommender = LocationRecommender(self.posts_df)
        
        self.content_recommender = ContentRecommender(
            self.posts_df,
            self.user_favorites_df
        )
        
        self.collaborative_recommender = CollaborativeRecommender(
            self.user_interactions
        )
        
        self.popularity_recommender = PopularityRecommender(
            self.posts_df
        )
        
        self.hybrid_recommender = HybridRecommender(
            self.content_recommender,
            self.collaborative_recommender
        )
    
    def recommend(self, user_id=None, lat=None, lon=None, distance_km=10, method='location'):

        if self.posts_df is None:
            self.load_data()

        if method == 'hybrid':
            recommendations = self.hybrid_recommender.get_recommendations(user_id, lat, lon, distance_km)
            score_col = 'final_score'
        elif method == 'location':
            if lat is None or lon is None:
                if user_id is not None:
                    user = self.users_df[self.users_df['id'] == user_id]
                    if not user.empty and not pd.isna(user['latitude'].iloc[0]) and not pd.isna(user['longitude'].iloc[0]):
                        lat = user['latitude'].iloc[0]
                        lon = user['longitude'].iloc[0]
                
                if lat is None or lon is None:
                    return pd.DataFrame()  
            
            recommendations = self.location_recommender.get_recommendations(lat, lon, distance_km)
            score_col = 'distance_km'
        elif method == 'content':
            recommendations = self.content_recommender.get_recommendations(user_id)
            score_col = 'similarity_score'
        elif method == 'collaborative':
            recommendations = self.collaborative_recommender.get_recommendations(user_id)
            score_col = 'predicted_score'
        elif method == 'popularity':
            recommendations = self.popularity_recommender.get_recommendations()
            score_col = 'popularity_score'
        else:
            raise ValueError(f"Unknown recommendation method: {method}")

        if recommendations.empty:
            return pd.DataFrame()
            
        post_ids = recommendations['post_id'].tolist()
        posts = self.posts_df[self.posts_df['id'].isin(post_ids)].copy()

        score_mapping = dict(zip(recommendations['post_id'], recommendations[score_col]))
        posts['recommendation_score'] = posts['id'].map(score_mapping)
        
        # For location-based recommendations, lower values are better
        is_ascending = (score_col == 'distance_km')
        posts = posts.sort_values('recommendation_score', ascending=is_ascending)
            
        return posts
    
    def __init__(self, db_connection):

        self.db_manager = DatabaseManager(db_connection)
        
        # Load data
        self.posts_df = None
        self.comments_df = None
        self.sub_comments_df = None
        self.users_df = None
        self.user_favorites_df = None
        self.user_interactions = None
        
        # Initialize recommenders
        self.location_recommender = None
        self.content_recommender = None
        self.collaborative_recommender = None
        self.popularity_recommender = None
        self.hybrid_recommender = None
        
    def load_data(self):

        self.posts_df = self.db_manager.load_posts()
        self.comments_df = self.db_manager.load_comments()
        self.sub_comments_df = self.db_manager.load_sub_comments()
        self.users_df = self.db_manager.load_users()
        self.user_favorites_df = self.db_manager.load_user_favorites()
        self.user_interactions = self.db_manager.load_user_interactions()

        self.location_recommender = LocationRecommender(self.posts_df)
        
        self.content_recommender = ContentRecommender(
            self.posts_df,
            self.user_favorites_df
        )
        
        self.collaborative_recommender = CollaborativeRecommender(
            self.user_interactions
        )
        
        self.popularity_recommender = PopularityRecommender(
            self.posts_df
        )
        
        self.hybrid_recommender = HybridRecommender(
            self.content_recommender,
            self.collaborative_recommender
        )
    
    def recommend(self, user_id=None, lat=None, lon=None, distance_km=10, method='location'):

        if self.posts_df is None:
            self.load_data()

        if method == 'hybrid':
            recommendations = self.hybrid_recommender.get_recommendations(user_id)
            score_col = 'final_score'
        elif method == 'location':
            if lat is None or lon is None:

                if user_id is not None:
                    user = self.users_df[self.users_df['id'] == user_id]
                    if not user.empty and not pd.isna(user['latitude'].iloc[0]) and not pd.isna(user['longitude'].iloc[0]):
                        lat = user['latitude'].iloc[0]
                        lon = user['longitude'].iloc[0]
                
                if lat is None or lon is None:
                    return pd.DataFrame()  
            
            recommendations = self.location_recommender.get_recommendations(lat, lon, distance_km)
            score_col = 'distance_km'
        elif method == 'content':
            recommendations = self.content_recommender.get_recommendations(user_id)
            score_col = 'similarity_score'
        elif method == 'collaborative':
            recommendations = self.collaborative_recommender.get_recommendations(user_id)
            score_col = 'predicted_score'
        elif method == 'popularity':
            recommendations = self.popularity_recommender.get_recommendations()
            score_col = 'popularity_score'
        else:
            raise ValueError(f"Unknown recommendation method: {method}")

        if recommendations.empty:
            return pd.DataFrame()
            
        post_ids = recommendations['post_id'].tolist()
        posts = self.posts_df[self.posts_df['id'].isin(post_ids)].copy()

        score_mapping = dict(zip(recommendations['post_id'], recommendations[score_col]))
        posts['recommendation_score'] = posts['id'].map(score_mapping)

        is_ascending = (score_col == 'distance_km')
        posts = posts.sort_values('recommendation_score', ascending=is_ascending)

            
        return posts