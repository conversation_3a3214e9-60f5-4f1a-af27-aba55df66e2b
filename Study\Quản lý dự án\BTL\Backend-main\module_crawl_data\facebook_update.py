import requests
import json
import time
from datetime import datetime, timedelta
import mysql.connector
import re
from database import DatabaseManager

class FacebookUpdater:
    def __init__(self, cookie, urls, mysql_config):
        self.cookie = cookie
        self.urls = urls
        self.mysql_config = mysql_config
        self.db_manager = DatabaseManager(mysql_config)
        self.access_token = self._get_access_token()
        
        self.post_fields = 'id,parent_id,created_time,permalink_url,full_picture,shares,reactions.summary(total_count),attachments{subattachments.limit(20)},message'
        self.comment_fields = 'comments.order(chronological).summary(total_count){id,created_time,reactions.summary(total_count),message,comment_count,comments}'

    def _get_access_token(self):
        session = requests.Session()
        response = session.get('https://business.facebook.com/business_locations', headers={'cookie': self.cookie})
        if response.status_code == 200:
            search_token = re.search(r'(EAAG\w+)', response.text)
            if search_token:
                return search_token.group(1)
        
        print('Cannot find access token. Maybe your cookie is invalid !!')
        return None

    def _fetch_post_details(self, post_id):
        if not self.access_token:
            print("No access token available")
            return None

        fields = f'{self.post_fields},{self.comment_fields}'
        endpoint = f'https://graph.facebook.com/v18.0/{post_id}?fields={fields}&access_token={self.access_token}'
        
        try:
            response = requests.get(endpoint, headers={'cookie': self.cookie})
            if response.status_code == 404:
                return 'NOT_FOUND'
            elif response.status_code == 200:
                return json.loads(response.text)
            else:
                print(f"Error response from Facebook API: {response.status_code}")
                # Return special value to indicate API error
                return 'API_ERROR'
        except Exception as e:
            print(f"Error fetching post details: {e}")
            return None

    def _delete_post_from_database(self, post_id, connection, cursor):
        try:
            # First delete related comments
            cursor.execute("DELETE FROM comments WHERE post_id = %s", (post_id,))
            # Then delete the post
            cursor.execute("DELETE FROM posts WHERE id = %s", (post_id,))
            connection.commit()
            return True
        except mysql.connector.Error as err:
            print(f"Error deleting post {post_id}: {err}")
            connection.rollback()
            return False

    def update_facebook_data(self, stop_update):
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor(dictionary=True)
            # Fetch all posts from the database
            cursor.execute("SELECT id FROM posts")
            posts = cursor.fetchall()

            updated_count = 0
            deleted_count = 0
            images_updated_count = 0
            
            for post in posts:
                if stop_update.is_set():
                    print("Update process stopped")
                    break

                post_id = post['id']
                
                # Fetch latest post details from Facebook
                updated_post_data = self._fetch_post_details(post_id)
                
                if updated_post_data == 'API_ERROR':
                    print(f"Stopping thread due to Facebook API error for post {post_id}")
                    # stop_update.set()  
                    break
                elif updated_post_data == 'NOT_FOUND':
                    # Post no longer exists on Facebook, delete it from database
                    if self._delete_post_from_database(post_id, connection, cursor):
                        deleted_count += 1
                        print(f"Deleted post {post_id} - no longer exists on Facebook")
                elif updated_post_data:
                    # Update image URLs first
                    images_updated = self._update_image_urls(post_id, updated_post_data, connection, cursor)
                    if images_updated > 0:
                        images_updated_count += images_updated
                        print(f"Updated {images_updated} images for post {post_id}")
                    
                    # Attempt to save the updated data
                    if self.db_manager.save_all_data_to_mysql(updated_post_data):
                        updated_count += 1
                        print(f"Updated post: {post_id}")
                        
                        # Print details about the changes
                        if 'comments' in updated_post_data:
                            comment_count = len(updated_post_data.get('comments', {}).get('data', []))
                            print(f"  - New comments: {comment_count}")
                        
                        if 'reactions' in updated_post_data:
                            reactions = updated_post_data.get('reactions', {}).get('summary', {}).get('total_count', 0)
                            print(f"  - Total reactions: {reactions}")
                
                # Avoid rate limiting
                time.sleep(1)

            print(f"Update complete. Total posts updated: {updated_count}, deleted: {deleted_count}, images updated: {images_updated_count} out of {len(posts)} posts")
            return updated_count, deleted_count, images_updated_count

        except mysql.connector.Error as err:
            print(f"Database error: {err}")
            return None
        finally:
            cursor.close()
            connection.close()

    def _update_image_urls(self, post_id, updated_post_data, connection, cursor):

        images_updated = 0
        
        try:
            # First check if post exists
            cursor.execute("SELECT id FROM posts WHERE id = %s", (post_id,))
            post_exists = cursor.fetchone()
            
            if not post_exists:
                # Post doesn't exist, delete all related image records
                cursor.execute("DELETE FROM post_images WHERE post_id = %s", (post_id,))
                cursor.execute("DELETE FROM sub_post_images WHERE post_id = %s", (post_id,))
                connection.commit()
                print(f"Deleted orphaned image records for non-existent post {post_id}")
                return 0
            
            # Update main post image
            if 'full_picture' in updated_post_data:
                new_image_url = updated_post_data['full_picture']
                cursor.execute("UPDATE posts SET full_picture = %s WHERE id = %s", 
                            (new_image_url, post_id))
                images_updated += 1
            
            # Update post_images table
            if 'attachments' in updated_post_data and 'data' in updated_post_data['attachments']:
                for attachment in updated_post_data['attachments']['data']:
                    if 'media' in attachment and 'image' in attachment['media']:
                        new_image_url = attachment['media']['image']['src']
                        
                        # Check if entry exists in post_images
                        cursor.execute("SELECT id FROM post_images WHERE post_id = %s", (post_id,))
                        image_records = cursor.fetchall()
                        
                        if image_records:
                            for image_record in image_records:
                                cursor.execute("UPDATE post_images SET full_picture_src = %s WHERE id = %s", 
                                            (new_image_url, image_record['id']))
                                images_updated += 1
                        else:
                            # Insert if no record exists
                            cursor.execute("INSERT INTO post_images (post_id, full_picture_src) VALUES (%s, %s)",
                                        (post_id, new_image_url))
                            images_updated += 1
            
            # Update sub_post_images table
            if 'attachments' in updated_post_data and 'data' in updated_post_data['attachments']:
                for attachment in updated_post_data['attachments']['data']:
                    if 'subattachments' in attachment and 'data' in attachment['subattachments']:
                        for subattachment in attachment['subattachments']['data']:
                            if 'media' in subattachment and 'image' in subattachment['media']:
                                new_image_url = subattachment['media']['image']['src']
                                image_height = subattachment['media']['image'].get('height', 0)
                                image_width = subattachment['media']['image'].get('width', 0)
                                target_id = subattachment.get('target', {}).get('id', '')
                                target_url = subattachment.get('target', {}).get('url', '')
                                attachment_type = subattachment.get('type', '')
                                
                                # Check if entry exists
                                cursor.execute(
                                    "SELECT id FROM sub_post_images WHERE post_id = %s AND target_id = %s", 
                                    (post_id, target_id)
                                )
                                sub_image_records = cursor.fetchall()
                                
                                if sub_image_records and len(sub_image_records) > 0:
                                    # Update all matching records
                                    for sub_image_record in sub_image_records:
                                        cursor.execute(
                                            "UPDATE sub_post_images SET image_src = %s, image_height = %s, image_width = %s, " 
                                            "target_url = %s WHERE id = %s",
                                            (new_image_url, image_height, image_width, target_url, sub_image_record['id'])
                                        )
                                else:
                                    cursor.execute(
                                        "INSERT INTO sub_post_images (post_id, image_height, image_width, image_src, "
                                        "target_id, target_url, type) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                                        (post_id, image_height, image_width, new_image_url, target_id, target_url, attachment_type)
                                    )
                                
                                images_updated += 1
            
            connection.commit()
            return images_updated
            
        except mysql.connector.Error as err:
            print(f"Error updating image URLs for post {post_id}: {err}")
            connection.rollback()
            return 0