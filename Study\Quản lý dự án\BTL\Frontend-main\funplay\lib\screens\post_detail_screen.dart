import 'package:flutter/material.dart';
import 'package:funplay/models/post.dart';
import 'package:funplay/models/post_detail.dart';
import 'package:funplay/api/post.dart';
import 'package:funplay/components/image_slider.dart';
import 'package:funplay/components/stats_section.dart';
import 'package:funplay/components/location_section.dart';
import 'package:funplay/components/rating_breakdown.dart';
import 'package:funplay/components/comment_section.dart';
import 'package:funplay/components/loading_state.dart';
import 'package:funplay/components/error_state.dart';

class PostDetailScreen extends StatefulWidget {
  final Post? post;

  const PostDetailScreen({Key? key, this.post}) : super(key: key);

  @override
  _PostDetailScreenState createState() => _PostDetailScreenState();
}

class _PostDetailScreenState extends State<PostDetailScreen> {
  late PostDetail _postDetail;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Create a dummy post detail while loading real data
    _postDetail = _createInitialPostDetail();
    print("PostDetailScreen initialized with post: ${_postDetail.id}");

    _fetchPostDetail(_postDetail.id);
  }

  PostDetail _createInitialPostDetail() {
    // Use widget.post to create a basic PostDetail or use dummy data
    if (widget.post != null) {
      return PostDetail(
        id: widget.post!.id,
        title: widget.post!.title,
        location: widget.post!.location ?? '',
        rate: widget.post!.rating ?? 0.0,
        sharesCount: 0,
        reactions: widget.post!.reactions ?? 0,
        fullPicture: widget.post!.image ?? '',
        latitude: widget.post!.latitude,
        longitude: widget.post!.longitude,
        createdTime: DateTime.now().toIso8601String(),
        message: widget.post!.description,
        images: [],
        subImages: [],
        commentsList: [],
      );
    } else {
      // Create a dummy trending post
      return PostDetail.dummyTrending(1, ratingScale: 5);
    }
  }

  Future<void> _fetchPostDetail(String postId) async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final postData = await PostApi.getPostDetail(postId);

      if (postData['success'] == true) {
        var jsonData = postData['0'];
        final postDetail = PostDetail.fromJson(jsonData);

        setState(() {
          _postDetail = postDetail;
          _isLoading = false;
        });
      } else {
        throw Exception('Invalid post data format');
      }
    } catch (error) {
      print("Error fetching post detail: $error");
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load post details. Please try again.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    print("Building PostDetailScreen with post: ${_postDetail.id}");

    if (_isLoading) {
      return LoadingState(onBack: () => Navigator.pop(context));
    }

    if (_errorMessage != null) {
      return ErrorState(
        errorMessage: _errorMessage!,
        onRetry: () => _fetchPostDetail(_postDetail.id),
        onBack: () => Navigator.pop(context),
      );
    }

    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            Column(
              children: [
                // Image Slider (update ImageSlider to use imageUrls from new model)
                ImageSlider(postDetail: _postDetail),

                // Scrollable content
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title Section
                        _buildTitleSection(),

                        // Stats row
                        StatsSection(postDetail: _postDetail),

                        // Restaurant and Location info
                        LocationSection(postDetail: _postDetail),

                        // Ratings breakdown
                        RatingBreakdown(postDetail: _postDetail),

                        // Post content
                        _buildPostContent(),

                        // Comment section
                        CommentSection(postDetail: _postDetail),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Back button
            _buildBackButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleSection() {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: Text(
        _postDetail.title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildPostContent() {
    // Replace <br /> tags with line breaks for rendering
    String formattedDescription = '';

    if (_postDetail.message != null) {
      // Split the text by <br /> and join with newline characters
      formattedDescription = _postDetail.message!.replaceAll('<br />', '\n');
    }

    return Container(
      padding: const EdgeInsets.all(15),
      margin: const EdgeInsets.only(top: 10, bottom: 20),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Bài viết',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            formattedDescription,
            style: const TextStyle(
              fontSize: 14,
              height: 1.5,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackButton() {
    return Positioned(
      top: 16,
      left: 16,
      child: GestureDetector(
        onTap: () => Navigator.pop(context),
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Center(
            child: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}