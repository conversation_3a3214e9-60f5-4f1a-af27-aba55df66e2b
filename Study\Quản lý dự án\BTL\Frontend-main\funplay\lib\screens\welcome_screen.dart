import 'package:flutter/material.dart';
import 'package:funplay/components/custom_button.dart';
import 'package:funplay/constants/colors.dart';
import 'package:funplay/constants/images.dart';
import 'package:funplay/constants/urls.dart';
import 'package:funplay/utils/auth_utils.dart';
import 'package:url_launcher/url_launcher.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({Key? key}) : super(key: key);

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      final token = await AuthUtils.getAuthToken();
      if (token != null) {
        // Navigate to main screen if user is already logged in
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/main');
        }
      }
    } catch (error) {
      debugPrint('Error checking auth status: $error');
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  void _handleGoogleSignIn() {
    debugPrint('Google Sign In pressed');
  }

  void _handleTermsPress() async {
    final Uri url = Uri.parse(AppUrls.termsOfService);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  void _handlePrivacyPress() async {
    final Uri url = Uri.parse(AppUrls.privacyPolicy);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get screen size
    final size = MediaQuery.of(context).size;
    final isSmallDevice = size.width < 375;
    final isTablet = size.width >= 768;

    if (isLoading) {
      return Container(
        color: AppColors.white,
        child: const Center(
          child: CircularProgressIndicator(
            color: AppColors.primary,
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 14,
            vertical: MediaQuery.of(context).padding.top > 0 ? 20 : 60,
          ),
          child: Column(
            crossAxisAlignment: isTablet ? CrossAxisAlignment.center : CrossAxisAlignment.start,
            children: [
              // Header
              SizedBox(
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: isTablet ? CrossAxisAlignment.center : CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to Fun Play',
                      style: TextStyle(
                        fontSize: isSmallDevice ? 24 : 30,
                        fontWeight: FontWeight.bold,
                        color: AppColors.black,
                        fontFamily: 'Poppins',
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Ready to explore? Let\'s go!',
                      style: TextStyle(
                        fontSize: isSmallDevice ? 14 : 16,
                        color: Gray.text,
                        fontFamily: 'Poppins',
                      ),
                    ),
                  ],
                ),
              ),

              // Illustration
              Expanded(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: isTablet ? 600 : double.infinity,
                  ),
                  child: Image.asset(
                    AppImages.welcomeBackground,
                    fit: BoxFit.contain,
                  ),
                ),
              ),

              // Bottom buttons
              Container(
                width: isTablet ? 400 : double.infinity,
                padding: const EdgeInsets.only(bottom: 40),
                child: Column(
                  children: [
                    // CustomButton(
                    //   title: 'Continue with Google',
                    //   variant: ButtonVariant.google,
                    //   onPress: _handleGoogleSignIn,
                    //   iconPath: AppImages.googleIcon,
                    //   margin: const EdgeInsets.only(bottom: 12),
                    // ),

                    Row(
                      children: [
                        Expanded(
                          child: CustomButton(
                            title: 'Log in',
                            variant: ButtonVariant.outline,
                            onPress: () => Navigator.of(context).pushNamed('/login'),
                            margin: const EdgeInsets.only(right: 8),
                          ),
                        ),
                        Expanded(
                          child: CustomButton(
                            title: 'I\'m new',
                            variant: ButtonVariant.primary,
                            onPress: () => Navigator.of(context).pushNamed('/signup'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}