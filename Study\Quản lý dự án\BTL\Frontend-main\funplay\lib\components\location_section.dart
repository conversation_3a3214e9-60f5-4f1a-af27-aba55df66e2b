import 'package:flutter/material.dart';
import 'package:funplay/models/post_detail.dart';
import 'package:funplay/constants/colors.dart';
import 'package:funplay/constants/images.dart';
import 'package:url_launcher/url_launcher.dart';

class LocationSection extends StatelessWidget {
  final PostDetail postDetail;

  const LocationSection({Key? key, required this.postDetail}) : super(key: key);

  bool get _hasValidCoordinates {
    return postDetail.latitude != null &&
        postDetail.longitude != null &&
        postDetail.latitude != 0 &&
        postDetail.longitude != 0;
  }

  void _launchPermalinkUrl() async {
    if (postDetail.permalinkUrl != null && postDetail.permalinkUrl!.isNotEmpty) {
      try {
        final url = Uri.parse(postDetail.permalinkUrl!);
        if (!await launchUrl(
          url,
          mode: LaunchMode.externalApplication,
          webViewConfiguration: const WebViewConfiguration(
            enableJavaScript: true,
            enableDomStorage: true,
          ),
        )) {
          print('Could not launch $url');
        }
      } catch (e) {
        print('Error launching URL: $e');
      }
    }
  }

  void _launchMapUrl() async {
    if (_hasValidCoordinates) {
      try {
        final latitude = postDetail.latitude;
        final longitude = postDetail.longitude;

        // Try to use a geo URI first (works better on Android)
        final geoUrl = Uri.parse('geo:$latitude,$longitude?q=$latitude,$longitude');

        if (await canLaunchUrl(geoUrl)) {
          await launchUrl(geoUrl);
        } else {
          // Fallback to web URL
          final mapUrl = Uri.parse('https://www.google.com/maps/search/?api=1&query=$latitude,$longitude');

          if (!await launchUrl(
            mapUrl,
            mode: LaunchMode.externalApplication,
            webViewConfiguration: const WebViewConfiguration(
              enableJavaScript: true,
              enableDomStorage: true,
            ),
          )) {
            print('Could not launch Google Maps');
          }
        }
      } catch (e) {
        print('Error launching map: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Kiểm tra xem có dữ liệu vị trí hoặc permalink để hiển thị không
    final bool hasLocationData = postDetail.location.isNotEmpty || _hasValidCoordinates;
    final bool hasPermalink = postDetail.permalinkUrl != null && postDetail.permalinkUrl!.isNotEmpty;

    // Nếu không có dữ liệu vị trí và permalink thì không hiển thị section này
    if (!hasLocationData && !hasPermalink) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 10),
      padding: const EdgeInsets.all(15),
      color: AppColors.white,
      child: Row(
        children: [
          // Left side with info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Permalink section
                if (hasPermalink)
                  GestureDetector(
                    onTap: _launchPermalinkUrl,
                    child: Row(
                      children: [
                        Icon(
                          Icons.link,
                          size: 24,
                          color: AppColors.primary,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'View original post',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.black,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ],
                    ),
                  ),
                if (hasPermalink && postDetail.location.isNotEmpty)
                  const SizedBox(height: 16),

                // Location info
                if (postDetail.location.isNotEmpty)
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 24,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          postDetail.location,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),

          // Right side with map preview if valid location data exists
          if (_hasValidCoordinates)
            GestureDetector(
              onTap: _launchMapUrl,
              child: Container(
                width: 150,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.grey[300],
                ),
                child: Center(
                  child: Icon(
                    Icons.map,
                    size: 40,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}