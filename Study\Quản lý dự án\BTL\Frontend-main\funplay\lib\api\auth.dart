import 'dart:convert';
import '../utils/api_utils.dart';
import '../utils/auth_utils.dart';
import 'config.dart';

class AuthApi {
  static Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      print(ApiConfig.apiBaseUrl);
      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiBaseUrl}/auth/login',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );
      print(response);
      return ApiUtils.handleApiResponse(response);
    } catch (error) {
      print(error);
      throw error;
    }
  }

  static Future<Map<String, dynamic>> googleSignIn(String googleToken) async {
    try {
      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiBaseUrl}/auth/google',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'token': googleToken,
        }),
      );

      return ApiUtils.handleApiResponse(response);
    } catch (error) {
      throw error;
    }
  }

  static Future<Map<String, dynamic>> register(
      String nameAccount, String email, String dateOfBirth, String password, String address) async {
    try {
      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiBaseUrl}/auth/register',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'name_account': nameAccount,
          'email': email,
          'date_of_birth': dateOfBirth,
          'password': password,
          'address': address
        }),
      );

      return ApiUtils.handleApiResponse(response);
    } catch (error) {
      print('Registration error: $error');
      throw error;
    }
  }

  static Future<Map<String, dynamic>> logout() async {
  try {
    // Lấy token xác thực từ storage
    final token = await AuthUtils.getAuthToken();
    
    if (token == null) {
      throw Exception('Not authorized, no token');
    }
    
    final response = await ApiUtils.fetchWithTimeout(
      '${ApiConfig.apiBaseUrl}/auth/logout',
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token', // Thêm token vào header
      },
      body: jsonEncode({}),
    );

    return ApiUtils.handleApiResponse(response);
  } catch (error) {
    throw error;
  }
}
}
