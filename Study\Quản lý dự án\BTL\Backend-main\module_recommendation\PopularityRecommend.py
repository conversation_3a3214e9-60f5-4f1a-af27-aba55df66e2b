import pandas as pd

class PopularityRecommender:
    """Handles popularity-based recommendations"""
    
    def __init__(self, posts_df):
        """
        Initialize popularity-based recommender
        
        Parameters:
        -----------
        posts_df : pandas DataFrame
            DataFrame containing posts data
        """
        self.posts_df = posts_df.copy()
        
    def _calculate_post_popularity(self):
        """Calculate popularity score for each post"""
        # Normalize reactions and shares to be between 0 and 1
        max_reactions = self.posts_df['reactions_total_count'].max()
        max_shares = self.posts_df['shares_count'].max()
        
        # Avoid division by zero
        if max_reactions == 0:
            max_reactions = 1
        if max_shares == 0:
            max_shares = 1
            
        # Calculate normalized popularity score
        self.posts_df['popularity_score'] = (
            0.4 * (self.posts_df['reactions_total_count'] / max_reactions) +
            0.3 * (self.posts_df['shares_count'] / max_shares) +
            0.3 * (self.posts_df['rate'] / 5.0)  # Assuming rate is on a 0-5 scale
        )
    
    def get_recommendations(self):
        """
        Get popularity-based recommendations
        
        Returns:
        --------
        recommendations : pandas DataFrame
            DataFrame with post_id and popularity_score columns
        """
        # Calculate popularity score
        self._calculate_post_popularity()
        
        # Sort by popularity score
        result = self.posts_df[['id', 'popularity_score']].rename(columns={'id': 'post_id'})
        return result.sort_values('popularity_score', ascending=False)