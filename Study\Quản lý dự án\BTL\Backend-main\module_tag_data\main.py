import argparse
import torch
import flask
from flask import Blueprint, jsonify
import os
from dotenv import load_dotenv
import utils
from RNN import RNN
from config import get_mysql_config
from database import DatabaseManager
import threading
import asyncio
import mysql.connector
from flask_cors import CORS 
load_dotenv()

# Global variables
model = None
vocab = None
device = None
analysis_thread = None
is_processing = False

def load_model(config_fpath):
    global model, vocab, device
    config = utils.get_config(config_fpath)
    
    model = torch.load(config["model_fpath"])
    vocab = torch.load(config["vocab_fpath"])
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.eval()

async def predict_sentiment(sentence):
    global model, vocab, device
    
    corpus = [sentence]
    tensor = vocab.corpus_to_tensor(corpus)[0].to(device)
    tensor = tensor.unsqueeze(1)
    length = [len(tensor)]
    length_tensor = torch.LongTensor(length)
    
    prediction = torch.sigmoid(model(tensor, length_tensor))
    # Convert 0-1 scale to 1-5 scale
    scaled_prediction = 1 + (prediction.item() * 4)
    # Round to nearest 0.1
    rounded_prediction = round(scaled_prediction * 10) / 10
    return rounded_prediction

# Create Flask app and blueprint
app = flask.Flask(__name__)
CORS(app)

api_v1 = Blueprint('api', __name__, url_prefix='/api/v1')
MYSQL_CONFIG = get_mysql_config()
db_manager = DatabaseManager(MYSQL_CONFIG)

def process_sentiment_for_table(table_name, connection):
    global is_processing

    def execute_query(query, params=None, fetch_all=False):
        # Create cursor with proper charset
        cursor = connection.cursor(dictionary=True)
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            result = cursor.fetchall() if fetch_all else None
            cursor.close()
            return result
        except mysql.connector.Error as err:
            print(f"Database query error: {err}")
            return None

    query = f"SELECT id, message FROM {table_name} WHERE isTaggedSentiment = 0"
    items = execute_query(query, fetch_all=True)
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        for item in items:
            if not is_processing:
                print(f"Processing stopped for {table_name}")
                break
                
            # Encode and decode to handle Unicode properly
            clean_message = item['message'].encode('utf-8').decode('utf-8')
            clean_message = clean_message.replace('<br />', ' ').strip()
            if not clean_message:
                continue
            
            sentiment_score = loop.run_until_complete(predict_sentiment(clean_message))
            
            # Determine sentiment status based on 1-5 scale
            if sentiment_score >= 4.5:
                status = 'excellent'
            elif sentiment_score >= 3.5:
                status = 'good'
            elif sentiment_score >= 2.5:
                status = 'normal'
            elif sentiment_score >= 1.5:
                status = 'bad'
            else:
                status = 'very bad'

            # Print detailed analysis
            print(f"[{table_name.upper()}] ID: {item['id']}")
            print(f"Message: {clean_message}")
            print(f"Status: {status}, Rate: {sentiment_score}\n")
            
            # Update query based on table
            update_query = f"""
                UPDATE {table_name} 
                SET status = %s, rate = %s, isTaggedSentiment = 1 
                WHERE id = %s
                """
            execute_query(update_query, (status, sentiment_score, item['id']))
    finally:
        loop.close()

def run_sentiment_analysis():
    global is_processing
    
    connection = db_manager.create_mysql_connection()
    if connection:
        try:
            # Set connection character set to utf8mb4
            connection.set_charset_collation('utf8mb4', 'utf8mb4_unicode_ci')
            
            process_sentiment_for_table('posts', connection)
            process_sentiment_for_table('comments', connection)
            process_sentiment_for_table('sub_comments', connection)
            connection.commit()
        except Exception as e:
            print(f"Error processing sentiment: {e}")
        finally:
            connection.close()
            is_processing = False

@api_v1.route('/tag-data', methods=['POST'])
def tag_data():
    global analysis_thread, is_processing
    
    if is_processing:
        return jsonify({
            "status": "error",
            "message": "Sentiment analysis is already running"
        }), 409
    
    is_processing = True
    analysis_thread = threading.Thread(target=run_sentiment_analysis, daemon=True)
    analysis_thread.start()
    
    return jsonify({
        "status": "success",
        "message": "Sentiment analysis started"
    }), 202

@api_v1.route('/stop-tag-data', methods=['POST'])
def stop_tag_data():
    global is_processing
    
    if not is_processing:
        return jsonify({
            "status": "error",
            "message": "No sentiment analysis is currently running"
        }), 404
    
    is_processing = False
    return jsonify({
        "status": "success",
        "message": "Sentiment analysis stopping"
    }), 200

@api_v1.route('/tag-data-status', methods=['GET'])
def tag_data_status():
    global is_processing
    
    # Create a connection to check tagging status
    connection = db_manager.create_mysql_connection()
    if not connection:
        return jsonify({
            "status": "error",
            "message": "Database connection failed"
        }), 500
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Count tagged and total items for each table
        tables = ['posts', 'comments', 'sub_comments']
        tag_counts = {}
        
        for table in tables:
            # Count total and tagged items
            cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_count, 
                    SUM(CASE WHEN isTaggedSentiment = 1 THEN 1 ELSE 0 END) as tagged_count 
                FROM {table}
            """)
            result = cursor.fetchone()
            tag_counts[table] = {
                'total': result['total_count'],
                'tagged': result['tagged_count'] or 0
            }
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "status": "running" if is_processing else "stopped",
            "message": "Sentiment analysis is running" if is_processing else "Sentiment analysis is not running",
            "tag_counts": tag_counts
        }), 200
    
    except mysql.connector.Error as err:
        print(f"Database error: {err}")
        return jsonify({
            "status": "error",
            "message": "Error fetching tagging status"
        }), 500
    finally:
        if connection and connection.is_connected():
            connection.close()

app.register_blueprint(api_v1)

def main(config_fpath):
    load_model(config_fpath)
    app.run(host="0.0.0.0", port=5010, debug=True)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Sentiment Prediction Flask API")
    parser.add_argument("--config", 
                        default="configs/predict_config.yml", 
                        help="path to config file",
                        dest="config_fpath")
    
    args = parser.parse_args()
    main(**vars(args))