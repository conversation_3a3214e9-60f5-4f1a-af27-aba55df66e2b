import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiUtils {
  static Future<http.Response> fetchWithTimeout(
      String url, {
        String method = 'GET',
        Map<String, String>? headers,
        Map<String, dynamic>? queryParams,
        dynamic body,
        int timeout = 10000,
      }) {
    Uri uri;

    // Handle query parameters for GET requests
    if (queryParams != null && queryParams.isNotEmpty) {
      uri = Uri.parse(url).replace(
        queryParameters: queryParams.map((key, value) =>
            MapEntry(key, value.toString())),
      );
    } else {
      uri = Uri.parse(url);
    }

    print('Request URL: $uri');

    Future<http.Response> request;

    switch (method) {
      case 'POST':
        request = http.post(uri,
            headers: headers, body: body is String ? body : jsonEncode(body));
        break;
      case 'PUT':
        request = http.put(uri,
            headers: headers, body: body is String ? body : jsonEncode(body));
        break;
      case 'DELETE':
        request = http.delete(uri,
            headers: headers, body: body is String ? body : jsonEncode(body));
        break;
      case 'PATCH':
        request = http.patch(uri,
            headers: headers, body: body is String ? body : jsonEncode(body));
        break;
      default:
        request = http.get(uri, headers: headers);
    }

    return request.timeout(Duration(milliseconds: timeout), onTimeout: () {
      throw TimeoutException('Request timeout');
    });
  }

  static Future<Map<String, dynamic>> handleApiResponse(
      http.Response response) async {
    final Map<String, dynamic> data = jsonDecode(response.body);

    if (response.statusCode < 200 || response.statusCode >= 300) {
      final error = Exception(data['message'] ?? 'Something went wrong');
      throw error;
    }

    return data;
  }
}