# -*- coding: utf-8 -*-

import re
import requests
import os
from dotenv import load_dotenv
import json

load_dotenv()
GEO_KEY = os.getenv("GEO_KEY")

def load_location_data():
    try:
        with open(os.path.join("Data", "city.txt"), encoding="utf-8") as f:
            cities = [line.strip() for line in f if line.strip()]
        with open(os.path.join("Data", "dist.txt"), encoding="utf-8") as f:
            districts = [line.strip() for line in f if line.strip()]
        with open(os.path.join("Data", "ward.txt"), encoding="utf-8") as f:
            wards = [line.strip() for line in f if line.strip()]
        return cities, districts, wards
    except Exception as e:
        return [], [], []

def get_noise_patterns():
    return [
        r'\b\d+k\b', r'\b\d+K\b', r'\b\d+đ\b',
        r'\bAM\b', r'\bPM\b',
        r'\bmenu\b',
        r'\bchill\b',
        r'\b086\b', r'\b2222\b',
        r'\b(?:đồng|cốc|vị|tê|ăn|mặt|tiền|tầng|giá|mua|open|close|fanpage|ý tưởng|lấy|chiếc|vòng|nguyệt quế)\b',
        r'\b(?:mùa đông|series|phiên bản|toanh|ra mắt|sản phẩm|trà sữa|tiếp tục|tiếp nối|thành công)\b',
        r'\b(?:20\d\d)\b',
        r'\b\d+/\d+\b',
        r'\b(?:ngon|hơn|đặc trưng|best seller|mix|cùng|quyết định|làm|mới|yêu thích|khách hàng|dành cho)\b',
        r'\b(?:chính thức|giá dùng thử|khi mua|cốc|đến ngày|và đã|mở bán|trên|tất cả|các|cơ sở|rùi nha)\b',
        r'\b(?:suất|miếng|cốc|ly|phần|gói|tô|đĩa|chiếc)\b',
        r'\b(?:ăn|uống|nếm|thưởng thức|vị|hương vị)\b',
        r'\b(?:bữa|buổi)\s+(?:sáng|trưa|tối|chiều)\b',
        r'\b(?:đồ|món)\s+(?:ăn|uống)\b',
        
        # Common phrases with numbers that aren't addresses
        r'\b\d+\s+(?:lần|suất|phút|giờ|ngày|tuần|tháng|năm)\b',
        r'\b\d+\s+(?:đồng|nghìn|k|triệu|tỷ)\b',
        r'\b\d+\s+(?:người|khách|bạn|bàn)\b',
    ]

def get_abbreviation_map():
    return {
        r"\bTP\.\s*HCM\b": "Thành phố Hồ Chí Minh",
        r"\bTP\.HCM\b": "Thành phố Hồ Chí Minh",
        r"\bHCM\b": "Hồ Chí Minh",
        r"\bSG\b": "Sài Gòn",
        r"\bHN\b": "Hà Nội",
        r"\bHà N~\b": "Hà Nội",
        r"\bTp\.HN\b": "Thành phố Hà Nội",
        r"\bQ\.\s*(\d+|\w+)\b": r"Quận \1",
        r"\bH\.\s*(\w+)\b": r"Huyện \1",
        r"\bP\.\s*(\w+)\b": r"Phường \1",
        r"\bTX\.\s*(\w+)\b": r"Thị xã \1",
        r"\bTp\.\s*(\w+)\b": r"Thành phố \1",
        r"\bTP\.\s*(\w+)\b": r"Thành phố \1",
        r"\bF\.\s*(\d+|\w+)\b": r"Phường \1",
        r"\bD\.\s*(\d+|\w+)\b": r"Quận \1"
    }

def preprocess_text(text):
    processed_text = re.sub(r'<br\s*/?>', ' ', text)
    processed_text = re.sub(r'<[^>]+>', ' ', processed_text)
    processed_text = re.sub(r'[🏡💵⏰📍🎄🎀✨❄️]', ' ', processed_text)
    
    processed_text = re.sub(r'\b\d{3,4}[\s-]?\d{3,4}[\s-]?\d{3,4}\b', ' ', processed_text)
    processed_text = re.sub(r'[𝟎-𝟙]', lambda m: str('0123456789'['𝟎𝟏𝟐𝟑𝟒𝟓𝟔𝟕𝟖𝟗'.index(m.group(0))]), processed_text)
    
    # Remove specific product/store name replacements
    return processed_text

def split_into_segments(text):
    sentence_endings = r'(?<!\d)\.\s+|\s*[?!;]\s+'
    return re.split(sentence_endings, text)

def replace_abbreviations(segments, abbreviation_map):
    processed_segments = []
    for segment in segments:
        processed_segment = segment
        for pattern, replacement in abbreviation_map.items():
            processed_segment = re.sub(pattern, replacement, processed_segment)
        processed_segments.append(processed_segment)
    return processed_segments

def find_street_entities(processed_text, noise_patterns):
    street_entities = []
    
    street_indicators = [
        r'đường', r'phố', r'ngõ', r'hẻm', r'ngách', 
        r'lô', r'số', r'tòa nhà', r'chung cư'
    ]
    
    street_patterns = [
        r'\b(\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)\s+'
        r'(?:' + '|'.join(street_indicators) + r')?\s*'
        r'([A-Za-zÀ-ỹ][A-Za-zÀ-ỹ\s]+)\.?',
        
        r'\b(\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)\s+'
        r'([A-Za-zÀ-ỹ][A-Za-zÀ-ỹ\s]+)\.?',
        
        r'\b(?:Số|số)\s*(\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)\s*,?\s*'
        r'(?:ngách\s*\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)?\s*,?\s*'
        r'(?:ngõ\s*\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)?\s*,?\s*'
        r'(?:hẻm\s*\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)?\s*,?\s*'
        r'([A-Za-zÀ-ỹ][A-Za-zÀ-ỹ\s]+)',

        r'\b(?:ngõ|ngách|hẻm)\s*(\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)\s*,?\s*'
        r'([A-Za-zÀ-ỹ][A-Za-zÀ-ỹ\s]+)',
        
        r'\b(\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)\s+'
        r'([A-Za-zÀ-ỹ][A-Za-zÀ-ỹ\s]+)',

        r'\b(?:Tim to tea|Fata Coffee|Cà phê\s+\w+|Quán\s+[A-Za-zÀ-ỹ\s]+).*?'
        r'(?:số|Số)?\s*(\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)\s*,?\s*'
        r'(?:ngõ|ngách|hẻm)?\s*(\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)\s*,?\s*'
        r'([A-Za-zÀ-ỹ][A-Za-zÀ-ỹ\s]+)',

        r'\b([A-Za-zÀ-ỹ][A-Za-zÀ-ỹ\s]+)\s*-\s*(\d+[A-Za-z]?(?:/\d+[A-Za-z]?)*)\s*,?\s*'
        r'([A-Za-zÀ-ỹ][A-Za-zÀ-ỹ\s]+)',
    ]
    
    for pattern in street_patterns:
        for match in re.finditer(pattern, processed_text, re.IGNORECASE):
            if match and match.groups():
                street_info = ' '.join(group for group in match.groups() if group)
                
                if any(re.search(noise, street_info, re.IGNORECASE) for noise in noise_patterns):
                    continue
                    
                if len(street_info) < 5 or not re.search(r'\d', street_info):
                    continue
                
                potential_street = match.groups()[1].strip().lower()
                if not any(indicator in potential_street for indicator in street_indicators):
                    # Check against common street name endings
                    if not re.search(r'(đường|phố|ngõ|hẻm|ngách|\bst\.|\bst\b|\bstreet\b)', potential_street):
                        continue
                    
                street_entities.append({
                    'start': match.start(),
                    'end': match.end(),
                    'text': street_info,
                    'type': 'STREET',
                    'original_position': match.start()
                })
    
    return street_entities

def find_location_entities(processed_text, cities, districts, wards):
    location_entities = []
    
    all_locations = []
    for city in cities:
        all_locations.append((city, 'CITY'))
    for district in districts:
        all_locations.append((district, 'DISTRICT'))
    for ward in wards:
        all_locations.append((ward, 'WARD'))
        
    all_locations.sort(key=lambda x: len(x[0]), reverse=True)

    for location, entity_type in all_locations:
        pattern = r'\b' + re.escape(location) + r'\b'
        for match in re.finditer(pattern, processed_text, re.IGNORECASE):
            if len(location) <= 2 and entity_type != 'WARD':
                continue
            
            entity_text = match.group()
            
            location_entities.append({
                'start': match.start(),
                'end': match.end(),
                'text': entity_text,
                'type': entity_type,
                'original_position': match.start()
            })
    
    return location_entities

def filter_unique_entities(all_entities):
    unique_entities = []
    seen_texts = set()
    
    for entity in sorted(all_entities, key=lambda x: x['original_position']):
        is_substring = False
        for other in all_entities:
            if (entity['text'] != other['text'] and 
                entity['text'] in other['text'] and 
                (other['start'] <= entity['start'] and other['end'] >= entity['end'])):
                is_substring = True
                break
                
        if not is_substring and entity['text'].lower() not in (text.lower() for text in seen_texts):
            unique_entities.append(entity)
            seen_texts.add(entity['text'].lower())
    
    return unique_entities

def group_entities(unique_entities):
    address_groups = []
    if unique_entities:
        sorted_entities = sorted(unique_entities, key=lambda x: x['original_position'])
        current_group = [sorted_entities[0]]
        
        for i in range(1, len(sorted_entities)):
            current_entity = sorted_entities[i]
            last_entity = current_group[-1]
            
            if current_entity['start'] - last_entity['end'] < 30:
                current_group.append(current_entity)
            else:
                if len(current_group) >= 2:
                    address_groups.append(current_group)
                current_group = [current_entity]
        
        if len(current_group) >= 2:
            address_groups.append(current_group)
    
    return address_groups

def find_house_number(processed_text, location_entity):
    MAX_CHARS_BEFORE = 10  # Maximum characters to look behind
    
    location_start = location_entity['start']
    if location_start <= 0:
        return None
        
    text_before = processed_text[max(0, location_start - MAX_CHARS_BEFORE):location_start].strip()
    
    # Check if there's a comma at the end of the text before
    if text_before and text_before[-1] == ',':
        text_before = text_before[:-1].strip()
    
    # Look for building/house number patterns
    house_number_patterns = [
        r'([A-Za-z0-9][A-Za-z0-9\-]+)',  # Simple alphanumeric with hyphens (e.g. D04-L1)
        r'(số\s+\d+[A-Za-z0-9\-/]*)',    # Vietnamese number format (e.g. số 123)
        r'(ngõ\s+\d+[A-Za-z0-9\-/]*)',   # Vietnamese alley format
        r'(ngách\s+\d+[A-Za-z0-9\-/]*)', # Vietnamese sub-alley format
        r'(hẻm\s+\d+[A-Za-z0-9\-/]*)',   # Vietnamese lane format
        r'(\d+[A-Za-z0-9\-/]*)'          # Just numbers with optional suffix (e.g. 123/45)
    ]
    
    for pattern in house_number_patterns:
        match = re.search(pattern, text_before)
        if match:
            return match.group(1)
    
    # If no pattern matches but text_before is short and looks like an address component
    if text_before and len(text_before) <= 10 and not re.search(r'\b(và|hoặc|hay|với|trong)\b', text_before):
        # Check if it contains any numbers or special characters typical for addresses
        if re.search(r'[0-9\-/]', text_before):
            return text_before
    
    return None

def evaluate_address_groups(address_groups, processed_text, noise_patterns):
    valid_addresses = []
    
    # Add a minimum confidence threshold
    MIN_CONFIDENCE = 5
    
    # List of address indicators in nearby context
    address_indicators = [
        r'địa\s*chỉ', r'tại', r'đến', r'chỗ', r'vị\s*trí', 
        r'cơ\s*sở', r'chi\s*nhánh', r'store', r'shop', r'quán',
        r'nhà\s*hàng', r'cửa\s*hàng', r'toà\s*nhà', r'tòa\s*nhà'
    ]
    
    # List of common food-related terms to filter out
    food_terms = [
        r'suất', r'miếng', r'cốc', r'ly', r'phần', r'gói', r'tô', r'đĩa', r'chiếc',
        r'ăn', r'uống', r'nếm', r'thưởng\s*thức', r'vị', r'hương\s*vị',
        r'bữa\s*(?:sáng|trưa|tối|chiều)', r'đồ\s*(?:ăn|uống)', r'món'
    ]
    
    for group in address_groups:
        entity_types = {entity['type'] for entity in group}
        
        if 'STREET' in entity_types or len(entity_types) >= 2:
            filtered_group = []
            seen = set()
            
            for entity in group:
                if entity['text'].lower() not in (text.lower() for text in seen):
                    filtered_group.append(entity)
                    seen.add(entity['text'].lower())
            
            # Check if we need to find a house number
            location_entities = [e for e in filtered_group if e['type'] in ['WARD', 'DISTRICT', 'CITY']]
            if location_entities and 'STREET' not in entity_types:
                # Try to find house number before the first location entity
                location_entities.sort(key=lambda x: x['original_position'])
                first_location = location_entities[0]
                house_number = find_house_number(processed_text, first_location)
                
                if house_number:
                    # Create a new entity for the house number
                    house_entity = {
                        'text': house_number,
                        'type': 'HOUSE_NUMBER',
                        'original_position': first_location['start'] - len(house_number) - 2  # Approximate position
                    }
                    filtered_group.append(house_entity)
            
            ordered_group = []
            for type_name in ['HOUSE_NUMBER', 'STREET', 'WARD', 'DISTRICT', 'CITY']:
                for entity in filtered_group:
                    if entity['type'] == type_name:
                        ordered_group.append(entity)
            
            if ordered_group:
                # Calculate base score
                score = len(ordered_group) + len(entity_types)
                
                # Increase score for better quality addresses
                if 'DISTRICT' in {e['type'] for e in ordered_group}:
                    if 'STREET' in {e['type'] for e in ordered_group} or 'CITY' in {e['type'] for e in ordered_group}:
                        score += 3
                
                # Increase score if it has a house number and street
                has_house_number = 'HOUSE_NUMBER' in {e['type'] for e in ordered_group}
                has_street = 'STREET' in {e['type'] for e in ordered_group}
                if has_house_number and has_street:
                    score += 2
                
                # Check for address indicators in nearby text
                if ordered_group:
                    context_start = max(0, ordered_group[0]['original_position'] - 50)
                    context_end = min(len(processed_text), ordered_group[-1]['original_position'] + 50)
                    context_text = processed_text[context_start:context_end].lower()
                    
                    # Increase score if address indicators are found
                    if any(re.search(indicator, context_text) for indicator in address_indicators):
                        score += 3
                    
                    # Decrease score if food-related terms are found
                    if any(re.search(term, context_text) for term in food_terms):
                        score -= 2
                
                # Get the address string
                address_parts = [entity['text'] for entity in ordered_group]
                address_str = ', '.join(address_parts)
                
                # Check for noise patterns
                has_noise = False
                for pattern in noise_patterns:
                    if re.search(pattern, address_str, re.IGNORECASE):
                        has_noise = True
                        break
                
                # Check for common non-address patterns
                if re.search(r'\d+\s+(?:lần|suất|phút|giờ|ngày|tuần|tháng|năm)', address_str, re.IGNORECASE):
                    has_noise = True
                
                # Reject if it's too short
                if len(address_str) < 10:
                    has_noise = True
                
                # Skip if confidence is too low
                if score < MIN_CONFIDENCE:
                    continue
                
                # Only add if it passes all checks
                if not has_noise:
                    valid_addresses.append({
                        'address': address_str,
                        'score': score,
                        'entity_count': len(ordered_group),
                        'has_street': has_street,
                        'has_house_number': has_house_number
                    })
    
    return valid_addresses

def extract_address_rule_based(text):
    # Step 1: Load location data
    cities, districts, wards = load_location_data()
    if not cities or not districts or not wards:
        return {"error": "Lỗi khi đọc file địa danh"}
    
    # Step 2: Get noise patterns and abbreviation map
    noise_patterns = get_noise_patterns()
    abbreviation_map = get_abbreviation_map()
    
    # Step 3: Preprocess text
    processed_text = preprocess_text(text)
    
    # Step 4: Split into segments
    segments = split_into_segments(processed_text)
    
    # Step 5: Replace abbreviations
    processed_segments = replace_abbreviations(segments, abbreviation_map)
    
    found_addresses = []
    
    # Step 6: Process each segment
    for segment_index, segment_text in enumerate(processed_segments):
        # Quick check for address patterns
        if not (re.search(r'\b\d+\s+[A-Za-zÀ-ỹ]', segment_text) or 
                re.search(r'\bQuận\b|\bPhường\b|\bHà Nội\b|\bHồ Chí Minh\b', segment_text)):
            continue
        
        # Step 6.1: Find street entities
        street_entities = find_street_entities(segment_text, noise_patterns)
        
        # Step 6.2: Find location entities
        location_entities = find_location_entities(segment_text, cities, districts, wards)
        
        # Step 6.3: Combine all entities
        all_entities = street_entities + location_entities
        
        # Skip if no entities found
        if not all_entities:
            continue
        
        # Step 6.4: Filter unique entities
        unique_entities = filter_unique_entities(all_entities)
        
        # Step 6.5: Group entities
        address_groups = group_entities(unique_entities)
        
        # Step 6.6: Evaluate and select valid address groups
        valid_addresses = evaluate_address_groups(address_groups, segment_text, noise_patterns)
        
        # Add valid addresses to results
        if valid_addresses:
            # Sort by score and entity count
            valid_addresses.sort(key=lambda x: (
                -x['has_street'], 
                -x['has_house_number'],
                -x['score'], 
                -x['entity_count']
            ))
            found_addresses.append(valid_addresses[0]['address'])
    
    # Step 7: Process final results
    if not found_addresses:
        return {"error": "Không tìm thấy thông tin địa chỉ trong văn bản."}
    
    # Remove duplicates
    unique_addresses = []
    for addr in found_addresses:
        if addr not in unique_addresses:
            unique_addresses.append(addr)
    
    # Otherwise, choose the longest address
    longest_address = max(unique_addresses, key=len)
    print(unique_addresses)
    return {"addresses": unique_addresses, "longest_address": longest_address}

def get_lat_long(address):
    try:
        print(f"Đang gửi yêu cầu đến API với địa chỉ: {address}")
        parameters = {
            "key": GEO_KEY,
            "location": address
        }
        response = requests.get("https://www.mapquestapi.com/geocoding/v1/address", params=parameters)
        data = json.loads(response.text)['results']
        return data
    except Exception as e:
        return {"error": f"Lỗi khi gọi API: {str(e)}"}
