import pandas as pd
import torch
from torch.utils.data import Dataset

class IMDBDataset(Dataset):
    """Load dataset from file csv"""

    def __init__(self, vocab, csv_fpath=None, tokenized_fpath=None):
        """
        @param vocab (Vocabulary)
        @param csv_fpath (str)
        @param tokenized_fpath (str)
        """
        self.vocab = vocab
        self.pad_idx = vocab["<pad>"]

        # Load the CSV file
        df = pd.read_csv(csv_fpath)
        print(f"Total rows read from dataset: {len(df)}")  # Print total rows read

        # Ensure `vi_review` is valid: Fill NaNs and convert to strings
        df['vi_review'] = df['vi_review'].fillna("").astype(str)

        # Identify and log empty reviews
        empty_review_indices = df[df['vi_review'].str.strip() == ""].index.tolist()
        if empty_review_indices:
            print(f"Found {len(empty_review_indices)} empty reviews. Removing them.")

        # Remove empty reviews
        df = df[df['vi_review'].str.strip() != ""]
        print(f"Rows remaining after removing empty reviews: {len(df)}")

        self.sentiments_list = list(df.sentiment)
        self.reviews_list = list(df.vi_review)

        # Extract unique sentiment types and create mapping
        sentiments_type = list(set(self.sentiments_list))
        sentiments_type.sort()
        self.sentiment2id = {sentiment: i for i, sentiment in enumerate(sentiments_type)}

        # Tokenize reviews or load pre-tokenized data
        if tokenized_fpath:
            self.tokenized_reviews = torch.load(tokenized_fpath)
        else:
            self.tokenized_reviews = self.vocab.tokenize_corpus(self.reviews_list)

        # Remove tokenized reviews with zero length
        valid_indices = [i for i, tokens in enumerate(self.tokenized_reviews) if len(tokens) > 0]
        print(f"Valid tokenized reviews: {len(valid_indices)} out of {len(self.tokenized_reviews)}")

        # Filter reviews and sentiments based on valid indices
        self.tokenized_reviews = [self.tokenized_reviews[i] for i in valid_indices]
        self.sentiments_list = [self.sentiments_list[i] for i in valid_indices]

        # Convert tokenized reviews and sentiments to tensors
        self.tensor_data = self.vocab.corpus_to_tensor(self.tokenized_reviews, is_tokenized=True)
        self.tensor_label = torch.tensor(
            [self.sentiment2id[sentiment] for sentiment in self.sentiments_list],
            dtype=torch.float64
        )

    def __len__(self):
        return len(self.tensor_data)

    def __getitem__(self, idx):
        return self.tensor_data[idx], self.tensor_label[idx]

    def collate_fn(self, examples):
        """
        Custom collate function to handle batching, padding, and sorting.
        """
        # Filter out examples with empty reviews
        examples = [e for e in examples if len(e[0]) > 0]
        if len(examples) == 0:
            raise ValueError("All reviews in the batch are empty!")

        # Sort examples by length of the review in descending order
        examples = sorted(examples, key=lambda e: len(e[0]), reverse=True)

        # Extract and pad reviews
        reviews = [e[0] for e in examples]
        reviews = torch.nn.utils.rnn.pad_sequence(
            reviews, batch_first=False, padding_value=self.pad_idx
        )

        # Extract lengths and labels
        reviews_lengths = torch.tensor([len(e[0]) for e in examples])
        sentiments = torch.tensor([e[1] for e in examples])

        return {"reviews": (reviews, reviews_lengths), "sentiments": sentiments}
