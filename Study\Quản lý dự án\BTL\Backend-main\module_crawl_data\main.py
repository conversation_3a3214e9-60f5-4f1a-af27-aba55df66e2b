from flask import Flask, Blueprint, jsonify, request
import os
import time
from queue import Queue
from dotenv import load_dotenv
from facebook_scraper import FacebookScraper
from config import get_mysql_config, get_urls
import threading
from flask_cors import CORS 
from facebook_update import FacebookUpdater

load_dotenv()

app = Flask(__name__)
CORS(app) 

# Tạo Blueprint với tiền tố '/api/v1'
api_v1 = Blueprint('api_v1', __name__, url_prefix='/api/v1')

scraping_thread = None
stop_scraping = threading.Event()

update_thread = None
stop_update = threading.Event()

MYSQL_CONFIG = get_mysql_config()
URLS = get_urls()

def get_cookie_from_request():
    # Lấy cookie từ params hoặc body
    cookie = request.args.get('cookie')
    if not cookie:
        # Nếu không có trong params, kiểm tra trong body
        if request.is_json:
            cookie = request.json.get('cookie')
    
    return cookie

def scrape_multiple_pages_or_groups(cookie, urls, mysql_config, error_queue, limit=10, max_posts=100, sleep=10, data_folder='data'):
    try:
        for url in urls:
            if stop_scraping.is_set(): 
                print(f"Stopping scraping process for: {url}")
                break
            print(f"Starting scrape for: {url}")
            scraper = FacebookScraper(cookie, url, mysql_config, limit, max_posts, sleep, data_folder)
            scraper.scrape_posts()
    except Exception as e:
        error_queue.put(str(e)) 

@api_v1.route('/scrape', methods=['GET'])
def scrape():
    global scraping_thread, stop_scraping

    try:
        cookie = get_cookie_from_request()
        if not cookie:
            return jsonify({
                "status": "error",
                "message": "Cookie is required. Please provide it in params or request body."
            }), 401
    
        if scraping_thread and scraping_thread.is_alive():
            return jsonify({
                "status": "error",
                "message": "Scraping is already in progress."
            }), 400

        stop_scraping.clear()

        error_queue = Queue()  

        try:
            scrape_multiple_pages_or_groups(cookie, URLS, MYSQL_CONFIG, error_queue)

            if not error_queue.empty():
                error_message = error_queue.get()
                return jsonify({
                    "status": "error",
                    "message": error_message
                }), 500
                
            return jsonify({
                "status": "success",
                "message": "Scraping completed successfully."
            }), 200
            
        except Exception as e:
            print(e)
            return jsonify({
                "status": "error",
                "message": str(e)
            }), 500

    except Exception as e:
        print(e)
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@api_v1.route('/stop-crawl', methods=['POST'])
def stop_scraping_process():
    global stop_scraping, scraping_thread

    try:
        if scraping_thread and scraping_thread.is_alive():

            stop_scraping.set()

            scraping_thread.join(timeout=5)

            return jsonify({
                "status": "success",
                "message": "Scraping process stopped successfully."
            }), 200
        else:
            return jsonify({
                "status": "error",
                "message": "No scraping process is currently running."
            }), 400

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

def update_multiple_pages_or_groups(cookie, urls, mysql_config, error_queue, stop_update):
    try:
        for url in urls:
            if stop_update.is_set(): 
                print(f"Stopping update process for: {url}")
                break
            print(f"Starting update for: {url}")
            updater = FacebookUpdater(cookie, urls, mysql_config)
            updater.update_facebook_data(stop_update)
    except Exception as e:
        error_queue.put(str(e)) 

@api_v1.route('/update', methods=['PUT'])
def update():
    global update_thread, stop_update

    try:
        cookie = get_cookie_from_request()
        if not cookie:
            return jsonify({
                "status": "error",
                "message": "Cookie is required. Please provide it in params or request body."
            }), 401
            
        if update_thread and update_thread.is_alive():
            return jsonify({
                "status": "error",
                "message": "Update is already in progress."
            }), 400

        stop_update.clear()

        error_queue = Queue()  

        try:
            update_multiple_pages_or_groups(cookie, URLS, MYSQL_CONFIG, error_queue, stop_update)
            
            if not error_queue.empty():
                error_message = error_queue.get()
                return jsonify({
                    "status": "error",
                    "message": error_message
                }), 500
            
            return jsonify({
                "status": "success",
                "message": "Update started successfully."
            }), 200
            
        except Exception as e:
            return jsonify({
                "status": "error",
                "message": str(e)
            }), 500
    except Exception as e:
        print(str(e))
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@api_v1.route('/stop-update', methods=['POST'])
def stop_update_process():
    global stop_update, update_thread

    try:
        if update_thread and update_thread.is_alive():

            stop_update.set()

            update_thread.join(timeout=5)

            return jsonify({
                "status": "success",
                "message": "Update process stopped successfully."
            }), 200
        else:
            return jsonify({
                "status": "error",
                "message": "No update process is currently running."
            }), 400

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500
        
app.register_blueprint(api_v1)

if __name__ == "__main__": 
    app.run(host="0.0.0.0", port=5000, debug=True)