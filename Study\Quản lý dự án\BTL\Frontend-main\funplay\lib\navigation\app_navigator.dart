import 'package:flutter/material.dart';
import 'package:funplay/screens/welcome_screen.dart';
import 'package:funplay/screens/login_screen.dart';
import 'package:funplay/screens/signup_screen.dart';
import 'package:funplay/screens/home_screen.dart';
import 'package:funplay/screens/spin_screen.dart';
import 'package:funplay/screens/collections_screen.dart';
import 'package:funplay/screens/vlog_screen.dart';
import 'package:funplay/screens/profile_screen.dart';
import 'package:funplay/screens/post_detail_screen.dart';
import 'package:funplay/constants/colors.dart';
import 'package:funplay/constants/images.dart';

class AppNavigator extends StatelessWidget {
  const AppNavigator({super.key});

  @override
  Widget build(BuildContext context) {
    return Navigator(
      initialRoute: '/',
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/':
            return MaterialPageRoute(
                builder: (context) => const WelcomeScreen());
          case '/login':
            return MaterialPageRoute(builder: (context) => const LoginScreen());
          case '/signup':
            return MaterialPageRoute(
                builder: (context) => const SignupScreen());
          case '/main':
            return MaterialPageRoute(
                builder: (context) => const MainTabNavigator());
          case '/post-detail':
            return MaterialPageRoute(
                builder: (context) => const PostDetailScreen());
          default:
            return MaterialPageRoute(
              builder: (context) => const WelcomeScreen(),
              settings: settings,
            );
        }
      },
    );
  }
}

class MainTabNavigator extends StatefulWidget {
  const MainTabNavigator({super.key});

  @override
  State<MainTabNavigator> createState() => _MainTabNavigatorState();
}

class _MainTabNavigatorState extends State<MainTabNavigator> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    HomeScreen(),
    SpinScreen(),
    CollectionsScreen(),
    // VlogScreen(),
    ProfileScreen(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        items: <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Image.asset(
              _selectedIndex == 0
                  ? AppImages.homeIconActive
                  : AppImages.homeIcon,
              width: 24,
              height: 24,
            ),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Image.asset(
              _selectedIndex == 1
                  ? AppImages.spinIconActive
                  : AppImages.spinIcon,
              width: 24,
              height: 24,
            ),
            label: 'Spin',
          ),
          BottomNavigationBarItem(
            icon: Image.asset(
              _selectedIndex == 2
                  ? AppImages.collectionsIconActive
                  : AppImages.collectionsIcon,
              width: 24,
              height: 24,
            ),
            label: 'Collections',
          ),
          // BottomNavigationBarItem(
          //   icon: Image.asset(
          //     _selectedIndex == 3
          //         ? AppImages.vlogIconActive
          //         : AppImages.vlogIcon,
          //     width: 24,
          //     height: 24,
          //   ),
          //   label: 'Vlog',
          // ),
          BottomNavigationBarItem(
            icon: Image.asset(
              _selectedIndex == 3
                  ? AppImages.profileIconActive
                  : AppImages.profileIcon,
              width: 24,
              height: 24,
            ),
            label: 'Profile',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: Gray.text,
        onTap: _onItemTapped,
        type: BottomNavigationBarType.fixed,
        selectedFontSize: 12,
        unselectedFontSize: 12,
        iconSize: 24,
      ),
    );
  }
}
