import React, { useEffect } from 'react';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { startCrawling } from '../services/api';
import { useAppContext } from '../context/AppContext';

const DataCrawler = () => {
  const {
    isCrawling, setIsCrawling,
    crawlStatus, setCrawlStatus,
    setCrawlError,
    lastCrawlTime, setLastCrawlTime
  } = useAppContext();

  const [cookie, setCookie] = React.useState(() => {
    return localStorage.getItem('facebookCookie') || '';
  });
  
  const [tempCookie, setTempCookie] = React.useState(() => {
    return localStorage.getItem('facebookCookie') || '';
  });

  const handleCookieSubmit = (e) => {
    e.preventDefault();
    if (tempCookie.trim() === '') {
      toast.error('<PERSON><PERSON> không được để trống', {
        position: "top-right",
        autoClose: 3000
      });
      return;
    }
    
    localStorage.setItem('facebookCookie', tempCookie);
    setCookie(tempCookie);
    toast.success('Cookie đã được lưu thành công!', {
      position: "top-right",
      autoClose: 3000
    });
  };

  const handleStartCrawl = async () => {
    if (!cookie) {
      toast.error('Vui lòng nhập cookie Facebook trước khi crawl dữ liệu', {
        position: "top-right",
        autoClose: 3000
      });
      return;
    }

    try {
      setIsCrawling(true);
      setCrawlStatus('Đang tiến hành crawl dữ liệu...');
      setCrawlError(null);
      
      // Lưu thời gian bắt đầu crawl
      const currentTime = new Date().toLocaleString();
      setLastCrawlTime(currentTime);
      
      // Gọi API để bắt đầu crawl với cookie
      await startCrawling(cookie);
      
      // Khi crawl xong
      setCrawlStatus('Crawl dữ liệu thành công!');
      setIsCrawling(false);
      
      toast.success('Crawl dữ liệu thành công!', {
        position: "top-right",
        autoClose: 3000
      });
    } catch (err) {
      const errorMessage = 'Lỗi khi crawl dữ liệu: ' + err.message;
      setCrawlError(errorMessage);
      setIsCrawling(false);
      setCrawlStatus('Crawl dữ liệu thất bại');
      
      toast.error(errorMessage, {
        position: "top-right",
        autoClose: 5000
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-3xl">
      <ToastContainer />

      {/* Cookie Section */}
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
        <form onSubmit={handleCookieSubmit} className="mb-2">
          <div className="relative mb-4">
            <textarea
              id="cookie"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 min-h-24 transition"
              value={tempCookie}
              onChange={(e) => setTempCookie(e.target.value)}
              placeholder="Nhập cookie Facebook của bạn"
            />
          </div>
          <button
            type="submit"
            className="bg-indigo-700 text-white rounded-lg px-2 py-2"
          >
            Lưu Cookie
          </button>
        </form>
        {cookie && (
          <div className="mt-2 text-green-600 flex items-center">
            <svg className="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
            Cookie đã được lưu
          </div>
        )}
      </div>

      {/* Status Section */}
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
        <h2 className="text-2xl font-semibold mb-4 text-indigo-600 flex items-center">
          Trạng thái Crawl
        </h2>
        <div className="flex items-center mb-4">
          <span className={`inline-block w-3 h-3 rounded-full mr-2 ${isCrawling ? "bg-yellow-500 animate-pulse" : crawlStatus.includes('thành công') ? "bg-green-500" : "bg-blue-500"}`}></span>
          <span className={`text-lg font-semibold ${isCrawling ? "text-yellow-600" : crawlStatus.includes('thành công') ? "text-green-600" : "text-blue-600"}`}>
            {crawlStatus}
          </span>
        </div>
        <div className="flex items-center mb-6">
          <span className="text-black py-1 rounded-full">
            Crawl lần cuối: {lastCrawlTime ? lastCrawlTime : 'Chưa có dữ liệu'}
          </span>
        </div>
        <div className="flex space-x-4">
          <button
            onClick={handleStartCrawl}
            disabled={isCrawling}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
              isCrawling
                ? "bg-gray-300 cursor-not-allowed"
                : "bg-indigo-700 shadow-md hover:shadow-lg text-white"
            }`}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
            {isCrawling ? "Đang Crawl..." : "Bắt đầu Crawl"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DataCrawler;