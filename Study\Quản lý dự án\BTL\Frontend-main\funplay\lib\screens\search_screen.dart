import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:funplay/screens/post_detail_screen.dart';
import '../constants/colors.dart';
import '../constants/images.dart';
import '../models/post.dart';
import '../models/comment.dart';
import '../api/search.dart';
import '../components/comment_item.dart';
import '../models/subcomment.dart';
import '../utils/user_preferences_utils.dart';
import 'package:intl/intl.dart';

class SearchScreen extends StatefulWidget {
  final String? initialQuery;

  const SearchScreen({Key? key, this.initialQuery}) : super(key: key);

  @override
  _SearchScreenState createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocus = FocusNode();
  final ScrollController _scrollController = ScrollController();

  // States for managing search and lazy loading
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasSearched = false;

  // Search results data
  List<Post> _searchResultPosts = [];
  List<Comment> _searchResultComments = [];
  List<SubComment> _searchResultSubComments = [];

  // Search query
  String _searchQuery = '';

  // Pagination state
  int _currentPage = 1;
  bool _hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);

    // Use the initialQuery if provided
    if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
      _searchController.text = widget.initialQuery!;
      // Delay the search to ensure the screen is fully loaded
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _performSearch(widget.initialQuery!);
      });
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _searchFocus.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocus.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  // Scroll listener for lazy loading
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 500 &&
        !_isLoadingMore &&
        _hasMoreData) {
      _loadMoreData();
    }
  }

  // Method to dismiss keyboard
  void _unfocusKeyboard() {
    _searchFocus.unfocus();
  }

  // Method to perform search
  Future<void> _performSearch(String query) async {
    if (query.isEmpty) return;

    setState(() {
      _isLoading = true;
      _hasSearched = true;
      _searchQuery = query;

      // Reset pagination when doing a new search
      _currentPage = 1;
      _hasMoreData = true;
    });

    try {
      // Get user data from preferences
      final userProfile = await UserPreferencesUtils.getUserProfile();
      final userId = userProfile['id'] ?? "1";

      // Fetch search results
      final searchResult = await SearchApi.search(query);

      setState(() {
        // Process search results
        _searchResultPosts = (searchResult['result']?['posts'] as List?)
            ?.map((post) => Post.fromJson(post))
            ?.toList() ?? [];

        _searchResultComments = (searchResult['result']?['comments'] as List?)
            ?.map((comment) => Comment(
          id: comment['id'] ?? '',
          content: comment['content'] ?? comment['message'] ?? '',
          userName: comment['user_name'] ?? comment['userName'] ?? '',
          userAvatar: comment['user_avatar'] ?? comment['userAvatar'] ?? 'assets/images/default_avatar.png',
          date: comment['created_time'] ?? '',
          postId: comment['post_id'] ?? comment['postId'],
          postTitle: comment['post_title'] ?? comment['postTitle'],
        ))
            ?.toList() ?? [];

        _searchResultSubComments = (searchResult['result']?['subComments'] as List?)
            ?.map((subComment) => SubComment.fromJson(subComment))
            ?.toList() ?? [];

        // Update hasMoreData flag
        _hasMoreData = _searchResultPosts.length >= 10;
      });
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Search failed: $error')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // Get user data from preferences
      final userProfile = await UserPreferencesUtils.getUserProfile();
      final userId = userProfile['id'] ?? "1";

      // Increment page
      _currentPage++;

      // For "All" tab, we fetch more search results
      final result = await SearchApi.search(_searchQuery);
      final newPosts = (result['result']?['posts'] as List?)
          ?.map((post) => Post.fromJson(post))
          ?.toList() ?? [];

      if (newPosts.isNotEmpty) {
        setState(() {
          _searchResultPosts.addAll(newPosts);
        });
      }

      setState(() {
        // Update hasMoreData flag
        _hasMoreData = newPosts.isNotEmpty && newPosts.length >= 10;
      });
    } catch (error) {
      print('Error loading more data: $error');
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _unfocusKeyboard,
      child: Scaffold(
        backgroundColor: AppColors.white,
        appBar: AppBar(
          backgroundColor: AppColors.redPrimary,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: AppColors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            'Search',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: Column(
          children: [
            // Search input field
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              color: AppColors.redPrimary,
              child: Container(
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(5),
                  border: Border.all(color: Gray.border),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      AppImages.search,
                      width: 20,
                      height: 20,
                      color: Gray.text,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        focusNode: _searchFocus,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.black,
                        ),
                        decoration: InputDecoration(
                          hintText: 'What do you think?',
                          hintStyle: TextStyle(
                            color: Gray.text,
                            fontSize: 14,
                          ),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                        ),
                        textInputAction: TextInputAction.search,
                        onSubmitted: (value) {
                          if (value.isNotEmpty) {
                            _performSearch(value);
                          }
                        },
                      ),
                    ),
                    if (_searchController.text.isNotEmpty)
                      GestureDetector(
                        onTap: () {
                          _searchController.clear();
                        },
                        child: Icon(
                          Icons.close,
                          size: 20,
                          color: Gray.text,
                        ),
                      ),
                  ],
                ),
              ),
            ),

            // Show search results or initial state
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasSearched
                  ? _buildSearchResults()
                  : _buildInitialState(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    // Return early if no search results
    if (_searchResultPosts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Gray.light),
            const SizedBox(height: 16),
            Text(
              'No results found for "$_searchQuery"',
              style: TextStyle(
                fontSize: 16,
                color: Gray.text,
              ),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        ListView.builder(
          controller: _scrollController,
          itemCount: _searchResultPosts.length,
          itemBuilder: (context, index) {
            return _buildPostItem(context, _searchResultPosts[index]);
          },
        ),

        // Show loading indicator at the bottom if loading more
        if (_isLoadingMore && _hasMoreData)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              color: Colors.white.withOpacity(0.8),
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.center,
              child: const CircularProgressIndicator(),
            ),
          ),
      ],
    );
  }

  // Initial state - empty search state
  Widget _buildInitialState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search, size: 48, color: Gray.light),
          const SizedBox(height: 16),
          Text(
            'Search for posts, comments, and replies',
            style: TextStyle(
              fontSize: 16,
              color: Gray.text,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter your search query above and press search',
            style: TextStyle(
              fontSize: 14,
              color: Gray.text,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostItem(BuildContext context, Post post) {
    return InkWell(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PostDetailScreen(post: post),
          ),
        );
      },
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: _buildImage(post.image),
                  ),
                  const SizedBox(width: 12),

                  // Post text content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          post.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.black,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),

                        // Location
                        Row(
                          children: [
                            Image.asset(
                              AppImages.location,
                              width: 14,
                              height: 14,
                              color: const Color(0xFFF4900C),
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                post.location,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Gray.text,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),

                        // Stats - Fixed layout to prevent overflow
                        Row(
                          children: [
                            // Rating
                            Image.asset(
                              AppImages.star,
                              width: 14,
                              height: 14,
                              color: const Color(0xFFF4900C),
                            ),
                            const SizedBox(width: 2),
                            Text(
                              post.rating.toString(),
                              style: TextStyle(
                                fontSize: 12,
                                color: Gray.text,
                              ),
                            ),
                            _buildDivider(),

                            // Comments
                            Flexible(
                              child: Text(
                                '${post.comments} comments',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Gray.text,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            _buildDivider(),

                            // Reactions
                            Flexible(
                              child: Text(
                                '${post.reactions} reacts',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Gray.text,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Favorite button
                  GestureDetector(
                    onTap: () {
                      // TODO: Handle favorite toggle
                      print("Tapping favorite on post: ${post.title}");
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Image.asset(
                        post.isFavorite ? AppImages.heartFilled : AppImages.heart,
                        width: 24,
                        height: 24,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Divider
          Container(
            height: 1,
            color: Gray.border,
          ),
        ],
      ),
    );
  }

  // Helper method to handle different image sources
  Widget _buildImage(String imageSource) {
    // Check if the imageSource is a URL or an asset path
    if (imageSource.startsWith('http')) {
      return Image.network(
        imageSource,
        width: 60,
        height: 60,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 60,
            height: 60,
            color: Colors.grey[300],
            child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
          );
        },
      );
    } else {
      return Image.asset(
        imageSource,
        width: 60,
        height: 60,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 60,
            height: 60,
            color: Colors.grey[300],
            child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
          );
        },
      );
    }
  }

  Widget _buildDivider() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Text(
        '|',
        style: TextStyle(
          fontSize: 12,
          color: Gray.light,
        ),
      ),
    );
  }
}