import 'package:flutter/material.dart';
import 'package:funplay/models/post_detail.dart';
import 'package:funplay/constants/colors.dart';

class RatingBreakdown extends StatelessWidget {
  final PostDetail postDetail;

  const RatingBreakdown({Key? key, required this.postDetail}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Define status categories
    final Map<String, Map<String, dynamic>> categories = {
      "excellent": {"label": "Excellent", "emoji": "😊", "count": 0},
      "good": {"label": "Good", "emoji": "😊", "count": 0},
      "normal": {"label": "Normal", "emoji": "😐", "count": 0},
      "bad": {"label": "Bad", "emoji": "😟", "count": 0},
    };

    // Get breakdown from post detail
    final breakdown = postDetail.getCommentRatingBreakdown();

    // Update counts in categories
    for (final entry in breakdown.entries) {
      if (categories.containsKey(entry.key)) {
        categories[entry.key]!['count'] = entry.value;
      }
    }

    // Convert the categories map to a list for display
    final ratingBreakdown = categories.entries.toList();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
      margin: const EdgeInsets.only(top: 10),
      color: AppColors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 10, bottom: 10),
            child: Text(
              'Rating Breakdown',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.black,
              ),
            ),
          ),
          Row(
            children: ratingBreakdown.map((entry) {
              final item = entry.value;
              return Expanded(
                child: Column(
                  children: [
                    Text(
                      item["emoji"] as String,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      item["label"] as String,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      (item["count"] as int).toString(),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}