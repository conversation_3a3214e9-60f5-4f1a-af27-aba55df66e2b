import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

class ContentRecommender:
    
    def __init__(self, posts_df, user_favorites_df):
        self.posts_df = posts_df
        self.user_favorites_df = user_favorites_df
        self.tfidf_matrix = None
        self.tfidf_feature_names = None
        self._create_content_matrix()
    
    def _create_content_matrix(self):
        self.posts_df['message'] = self.posts_df['message'].fillna('')
        tfidf = TfidfVectorizer(stop_words='english', min_df=2)
        try:
            self.tfidf_matrix = tfidf.fit_transform(self.posts_df['message'])
            self.tfidf_feature_names = tfidf.get_feature_names_out()
        except ValueError:
            self.tfidf_matrix = None
            self.tfidf_feature_names = None
    
    def get_recommendations(self, user_id):
        if self.tfidf_matrix is None:
            return pd.DataFrame(columns=['post_id', 'similarity_score'])
        user_favorites = self.user_favorites_df[self.user_favorites_df['user_id'] == user_id]
        if user_favorites.empty:
            return pd.DataFrame(columns=['post_id', 'similarity_score'])
        favorite_post_ids = user_favorites['post_id'].tolist()
        favorite_posts = self.posts_df[self.posts_df['id'].isin(favorite_post_ids)]
        if favorite_posts.empty:
            return pd.DataFrame(columns=['post_id', 'similarity_score'])
        favorite_indices = []
        for post_id in favorite_post_ids:
            post_indices = self.posts_df[self.posts_df['id'] == post_id].index
            if not post_indices.empty:
                favorite_indices.append(post_indices[0])
        if not favorite_indices:
            return pd.DataFrame(columns=['post_id', 'similarity_score'])
        user_profile = np.mean(
            [self.tfidf_matrix[idx].toarray().flatten() for idx in favorite_indices], 
            axis=0
        )
        user_profile = user_profile.reshape(1, -1)
        similarity_scores = cosine_similarity(user_profile, self.tfidf_matrix.toarray())[0]
        post_ids = self.posts_df['id'].tolist()
        result = []
        for i, post_id in enumerate(post_ids):
            if post_id not in favorite_post_ids:
                result.append({
                    'post_id': post_id,
                    'similarity_score': similarity_scores[i]
                })
        recommendations = pd.DataFrame(result)
        if recommendations.empty:
            return recommendations
        return recommendations.sort_values('similarity_score', ascending=False)
