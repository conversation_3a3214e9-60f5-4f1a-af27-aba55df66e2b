import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/auth_provider.dart';
import '../constants/colors.dart';
import '../constants/images.dart';
import '../constants/urls.dart';
import '../components/custom_button.dart';
import '../utils/auth_utils.dart';
import '../utils/user_preferences_utils.dart';

class SignupScreen extends StatefulWidget {
  const SignupScreen({Key? key}) : super(key: key);

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  final TextEditingController _addressController = TextEditingController(); // Thêm controller cho address
  final TextEditingController _passwordController = TextEditingController();

  bool _showPassword = false;
  bool _agreedToTerms = false;

  // Error messages
  String _nameError = '';
  String _emailError = '';
  String _dobError = '';
  String _addressError = ''; // Thêm thông báo lỗi cho address
  String _passwordError = '';
  String _termsError = '';

  bool _validateName(String name) {
    if (name.trim().isEmpty) {
      setState(() {
        _nameError = 'Name is required';
      });
      return false;
    }
    setState(() {
      _nameError = '';
    });
    return true;
  }

  bool _validateEmail(String email) {
    if (!AuthUtils.validateEmail(email)) {
      setState(() {
        _emailError = 'Please enter a valid email address';
      });
      return false;
    }
    setState(() {
      _emailError = '';
    });
    return true;
  }

  bool _validateDateOfBirth(String dob) {
    if (!AuthUtils.validateDateOfBirth(dob)) {
      setState(() {
        _dobError = 'Please enter a valid date (DD/MM/YYYY)';
      });
      return false;
    }
    setState(() {
      _dobError = '';
    });
    return true;
  }

  // Thêm validate cho address
  bool _validateAddress(String address) {
    if (address.trim().isEmpty) {
      setState(() {
        _addressError = 'Address is required';
      });
      return false;
    }
    setState(() {
      _addressError = '';
    });
    return true;
  }

  bool _validatePassword(String password) {
    if (!AuthUtils.validatePassword(password)) {
      setState(() {
        _passwordError = 'Password must be at least 8 characters with letters and numbers';
      });
      return false;
    }
    setState(() {
      _passwordError = '';
    });
    return true;
  }

  bool _validateTerms() {
    if (!_agreedToTerms) {
      setState(() {
        _termsError = 'You must agree to the terms and privacy policy';
      });
      return false;
    }
    setState(() {
      _termsError = '';
    });
    return true;
  }

  Future<void> _handleGoogleSignUp() async {
    debugPrint('Google Sign Up pressed');
    // Implement Google Sign-Up
  }

  Future<void> _handleSignUp() async {
    // Clear all error messages
    setState(() {
      _nameError = '';
      _emailError = '';
      _dobError = '';
      _addressError = ''; // Clear address error
      _passwordError = '';
      _termsError = '';
    });

    // Validate all fields
    final isNameValid = _validateName(_nameController.text);
    final isEmailValid = _validateEmail(_emailController.text);
    final isDobValid = _validateDateOfBirth(_dobController.text);
    final isAddressValid = _validateAddress(_addressController.text); // Validate address
    final isPasswordValid = _validatePassword(_passwordController.text);
    final isTermsValid = _validateTerms();

    if (!isNameValid || !isEmailValid || !isDobValid || !isAddressValid || !isPasswordValid || !isTermsValid) {
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    try {
      // Call register method from AuthProvider
      final success = await authProvider.register(
        _nameController.text,
        _emailController.text,
        _dobController.text,
        _addressController.text,
        _passwordController.text,
      );

      if (success) {
        // Clear input fields
        _nameController.clear();
        _emailController.clear();
        _dobController.clear();
        _addressController.clear();
        _passwordController.clear();

        // Navigate to main screen
        Navigator.of(context).pushReplacementNamed('/main');
        debugPrint('Registration successful');
      } else {
        // Handle specific error cases
        final error = authProvider.error ?? 'Unknown error occurred';

        // Show specific error messages based on the error received
        if (error.contains('email') && error.contains('already') && error.contains('exists')) {
          setState(() {
            _emailError = 'This email is already registered';
          });
        } else if (error.contains('password') && error.contains('weak')) {
          setState(() {
            _passwordError = 'Password is too weak. Please use a stronger password';
          });
        } else if (error.contains('network')) {
          _showErrorDialog('Network Error', 'Please check your internet connection and try again');
        } else {
          // Show generic error dialog for other errors
          _showErrorDialog('Registration Failed', error);
        }

        // Print detailed error for debugging
        print('Registration error: $error');
      }
    } catch (error) {
      // Log the specific error
      print('Exception during registration: $error');

      // Show error dialog with the specific error message
      _showErrorDialog('Registration Failed', error.toString());
    }
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _handleTermsPress() async {
    final url = AppUrls.termsOfService;
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  void _handlePrivacyPress() async {
    final url = AppUrls.privacyPolicy;
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _dobController.dispose();
    _addressController.dispose(); // Dispose address controller
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isLoading = authProvider.isLoading;
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: AppColors.white,
      resizeToAvoidBottomInset: true, // Đảm bảo màn hình scroll được khi hiện keyboard
      body: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Stack(
            children: [
              Column(
                children: [
                  // Back button
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                    child: Row(
                      children: [
                        InkWell(
                          onTap: () => Navigator.of(context).pop(),
                          child: Row(
                            children: [
                              const Icon(Icons.chevron_left, size: 24),
                              const SizedBox(width: 10),
                              Text(
                                'Sign Up',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Main content
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.06),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Name field
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                height: 56,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: _nameError.isNotEmpty ? Colors.red : Gray.border,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: TextField(
                                  controller: _nameController,
                                  decoration: InputDecoration(
                                    hintText: 'Name account',
                                    hintStyle: TextStyle(color: Gray.text),
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                                    border: InputBorder.none,
                                  ),
                                  enabled: !isLoading,
                                  onChanged: (text) {
                                    if (_nameError.isNotEmpty) {
                                      _validateName(text);
                                    }
                                  },
                                ),
                              ),
                              if (_nameError.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.only(top: 5, left: 5),
                                  child: Text(
                                    _nameError,
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Email field
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                height: 56,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: _emailError.isNotEmpty ? Colors.red : Gray.border,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: TextField(
                                  controller: _emailController,
                                  decoration: InputDecoration(
                                    hintText: 'Email',
                                    hintStyle: TextStyle(color: Gray.text),
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                                    border: InputBorder.none,
                                  ),
                                  keyboardType: TextInputType.emailAddress,
                                  autocorrect: false,
                                  enabled: !isLoading,
                                  onChanged: (text) {
                                    if (_emailError.isNotEmpty) {
                                      _validateEmail(text);
                                    }
                                  },
                                ),
                              ),
                              if (_emailError.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.only(top: 5, left: 5),
                                  child: Text(
                                    _emailError,
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Date of Birth field
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                height: 56,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: _dobError.isNotEmpty ? Colors.red : Gray.border,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: TextField(
                                  controller: _dobController,
                                  decoration: InputDecoration(
                                    hintText: 'Date of Birth (DD/MM/YYYY)',
                                    hintStyle: TextStyle(color: Gray.text),
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                                    border: InputBorder.none,
                                  ),
                                  keyboardType: TextInputType.datetime,
                                  enabled: !isLoading,
                                  onChanged: (text) {
                                    if (_dobError.isNotEmpty) {
                                      _validateDateOfBirth(text);
                                    }
                                  },
                                ),
                              ),
                              if (_dobError.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.only(top: 5, left: 5),
                                  child: Text(
                                    _dobError,
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Address field - Thêm trường address
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                height: 56,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: _addressError.isNotEmpty ? Colors.red : Gray.border,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: TextField(
                                  controller: _addressController,
                                  decoration: InputDecoration(
                                    hintText: 'Address',
                                    hintStyle: TextStyle(color: Gray.text),
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                                    border: InputBorder.none,
                                  ),
                                  enabled: !isLoading,
                                  onChanged: (text) {
                                    if (_addressError.isNotEmpty) {
                                      _validateAddress(text);
                                    }
                                  },
                                ),
                              ),
                              if (_addressError.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.only(top: 5, left: 5),
                                  child: Text(
                                    _addressError,
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Password field
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                height: 56,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: _passwordError.isNotEmpty ? Colors.red : Gray.border,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Stack(
                                  alignment: Alignment.centerRight,
                                  children: [
                                    TextField(
                                      controller: _passwordController,
                                      decoration: InputDecoration(
                                        hintText: 'Password',
                                        hintStyle: TextStyle(color: Gray.text),
                                        contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                                        border: InputBorder.none,
                                      ),
                                      obscureText: !_showPassword,
                                      autocorrect: false,
                                      enabled: !isLoading,
                                      onChanged: (text) {
                                        if (_passwordError.isNotEmpty) {
                                          _validatePassword(text);
                                        }
                                      },
                                    ),
                                    Positioned(
                                      right: 20,
                                      child: GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            _showPassword = !_showPassword;
                                          });
                                        },
                                        child: Icon(
                                          _showPassword ? Icons.visibility_off : Icons.visibility,
                                          color: Gray.text,
                                          size: 24,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (_passwordError.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.only(top: 5, left: 5),
                                  child: Text(
                                    _passwordError,
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Sign up button
                          SizedBox(
                            width: double.infinity,
                            height: 56,
                            child: CustomButton(
                              title: isLoading ? 'Signing Up...' : 'Sign Up',
                              variant: ButtonVariant.primary,
                              onPress: isLoading ? () {} : _handleSignUp,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Terms and conditions checkbox
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _agreedToTerms = !_agreedToTerms;
                                    if (_termsError.isNotEmpty) {
                                      _validateTerms();
                                    }
                                  });
                                },
                                child: Container(
                                  width: 20,
                                  height: 20,
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Gray.border),
                                    borderRadius: BorderRadius.circular(4),
                                    color: _agreedToTerms ? AppColors.primary : Colors.transparent,
                                  ),
                                  child: _agreedToTerms
                                      ? const Icon(Icons.check, size: 16, color: Colors.white)
                                      : null,
                                ),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: RichText(
                                  text: TextSpan(
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Gray.text,
                                      height: 1.5,
                                    ),
                                    children: [
                                      const TextSpan(text: "I agree to Fun's "),
                                      TextSpan(
                                        text: 'Terms of Service',
                                        style: TextStyle(
                                          color: AppColors.primary,
                                          decoration: TextDecoration.underline,
                                        ),
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = _handleTermsPress,
                                      ),
                                      const TextSpan(text: ' and confirm that I have read Fun\'s '),
                                      TextSpan(
                                        text: 'Privacy Policy',
                                        style: TextStyle(
                                          color: AppColors.primary,
                                          decoration: TextDecoration.underline,
                                        ),
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = _handlePrivacyPress,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (_termsError.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 5, left: 5),
                              child: Text(
                                _termsError,
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 12,
                                ),
                              ),
                            ),

                          // Illustration
                          Container(
                            height: screenSize.height * 0.25,
                            alignment: Alignment.center,
                            child: Image.asset(
                              AppImages.signupIllustration,
                              fit: BoxFit.contain,
                            ),
                          ),

                          // Add bottom padding for scrolling
                          SizedBox(height: 100),
                        ],
                      ),
                    ),
                  ),
                ],
              ),

              // Google sign up button - Fixed at the bottom
              // Positioned(
              //   left: 0,
              //   right: 0,
              //   bottom: 0,
              //   child: Container(
              //     padding: EdgeInsets.symmetric(
              //       horizontal: screenSize.width * 0.1,
              //       vertical: 20,
              //     ),
              //     decoration: BoxDecoration(
              //       color: AppColors.white,
              //       boxShadow: [
              //         BoxShadow(
              //           color: Colors.black.withOpacity(0.05),
              //           spreadRadius: 1,
              //           blurRadius: 10,
              //           offset: Offset(0, -5),
              //         ),
              //       ],
              //     ),
              //     child: SizedBox(
              //       width: double.infinity,
              //       height: 56,
              //       child: CustomButton(
              //         title: isLoading ? 'Please wait...' : 'Continue with Google',
              //         variant: ButtonVariant.google,
              //         iconPath: AppImages.googleIcon,
              //         onPress: isLoading ? () {} : _handleGoogleSignUp,
              //       ),
              //     ),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}