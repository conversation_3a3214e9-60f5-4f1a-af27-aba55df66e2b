import pandas as pd

class DatabaseManager:

    def __init__(self, db_connection):
        self.db_connection = db_connection
        
    def load_posts(self):
        return pd.read_sql("""
            SELECT 
                p.id, 
                p.created_time, 
                p.message, 
                p.reactions_total_count, 
                p.shares_count, 
                p.rate, 
                p.latitude, 
                p.longitude, 
                p.address, 
                p.full_picture, 
                p.permalink_url,
                (SELECT COUNT(*) FROM comments WHERE post_id = p.id) AS comments_count,
                (SELECT COUNT(*) FROM sub_comments sc 
                 JOIN comments c ON sc.parent_comment_id = c.id 
                 WHERE c.post_id = p.id) AS sub_comments_count
            FROM posts p
        """, self.db_connection)
        
    def load_comments(self):
        return pd.read_sql("""
            SELECT id, post_id, message, reactions_total_count
            FROM comments
        """, self.db_connection)
        
    def load_sub_comments(self):
        return pd.read_sql("""
            SELECT id, parent_comment_id, message
            FROM sub_comments
        """, self.db_connection)
        
    def load_users(self):
        return pd.read_sql("""
            SELECT id, email, name_account, latitude, longitude
            FROM users
        """, self.db_connection)
        
    def load_user_favorites(self):
        return pd.read_sql("""
            SELECT user_id, post_id
            FROM user_favorites
        """, self.db_connection)
    
    def load_user_interactions(self):
        comments_interactions = pd.read_sql("""
            SELECT u.id as user_id, c.post_id, 
                   COUNT(*) as interaction_strength
            FROM comments c
            JOIN users u ON c.message LIKE CONCAT('%', u.name_account, '%')
            GROUP BY u.id, c.post_id
        """, self.db_connection)

        subcomments_interactions = pd.read_sql("""
            SELECT u.id as user_id, c.post_id, 
                   COUNT(*) * 0.5 as interaction_strength
            FROM sub_comments sc
            JOIN comments c ON sc.parent_comment_id = c.id
            JOIN users u ON sc.message LIKE CONCAT('%', u.name_account, '%')
            GROUP BY u.id, c.post_id
        """, self.db_connection)

        all_interactions = pd.concat([comments_interactions, subcomments_interactions])
        return all_interactions.groupby(['user_id', 'post_id']).sum().reset_index()